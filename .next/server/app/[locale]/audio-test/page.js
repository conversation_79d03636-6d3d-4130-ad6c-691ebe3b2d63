/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/[locale]/audio-test/page";
exports.ids = ["app/[locale]/audio-test/page"];
exports.modules = {

/***/ "(rsc)/./src/i18n/locales lazy recursive ^\\.\\/.*\\.json$":
/*!****************************************************************!*\
  !*** ./src/i18n/locales/ lazy ^\.\/.*\.json$ namespace object ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./src/i18n/locales/en.json",
		"_rsc_src_i18n_locales_en_json"
	],
	"./zh.json": [
		"(rsc)/./src/i18n/locales/zh.json",
		"_rsc_src_i18n_locales_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./src/i18n/locales lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Faudio-test%2Fpage&page=%2F%5Blocale%5D%2Faudio-test%2Fpage&appPaths=%2F%5Blocale%5D%2Faudio-test%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Faudio-test%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Faudio-test%2Fpage&page=%2F%5Blocale%5D%2Faudio-test%2Fpage&appPaths=%2F%5Blocale%5D%2Faudio-test%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Faudio-test%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '[locale]',\n        {\n        children: [\n        'audio-test',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/audio-test/page.tsx */ \"(rsc)/./src/app/[locale]/audio-test/page.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/layout.tsx */ \"(rsc)/./src/app/[locale]/layout.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/[locale]/audio-test/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/[locale]/audio-test/page\",\n        pathname: \"/[locale]/audio-test\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Faudio-test%2Fpage&page=%2F%5Blocale%5D%2Faudio-test%2Fpage&appPaths=%2F%5Blocale%5D%2Faudio-test%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Faudio-test%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/AudioPlayer.tsx */ \"(ssr)/./src/components/AudioPlayer/AudioPlayer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/AudioPlayerProvider.tsx */ \"(ssr)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/PlayButton.tsx */ \"(ssr)/./src/components/AudioPlayer/PlayButton.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/ProgressBar.tsx */ \"(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/StandardPlayer.tsx */ \"(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AudioPlayer/VolumeControl.tsx */ \"(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2F%5Blocale%5D%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Noto_Sans_SC%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-noto-sans-sc%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22notoSansSC%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayer.tsx%22%2C%22ids%22%3A%5B%22AudioPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FAudioPlayerProvider.tsx%22%2C%22ids%22%3A%5B%22AudioPlayerProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FPlayButton.tsx%22%2C%22ids%22%3A%5B%22PlayButton%22%2C%22PrimaryPlayButton%22%2C%22SecondaryPlayButton%22%2C%22GhostPlayButton%22%2C%22LargePlayButton%22%2C%22SmallPlayButton%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FProgressBar.tsx%22%2C%22ids%22%3A%5B%22ProgressBar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FStandardPlayer.tsx%22%2C%22ids%22%3A%5B%22StandardPlayer%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fcomponents%2FAudioPlayer%2FVolumeControl.tsx%22%2C%22ids%22%3A%5B%22VolumeControl%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2F%5Blocale%5D%2Faudio-test%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2F%5Blocale%5D%2Faudio-test%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/[locale]/audio-test/page.tsx */ \"(ssr)/./src/app/[locale]/audio-test/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGb19vJTJGRG9jdW1lbnRzJTJGTm9pc2VTbGVlcCUyRnNyYyUyRmFwcCUyRiU1QmxvY2FsZSU1RCUyRmF1ZGlvLXRlc3QlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0xBQXlHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvP2JjM2MiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvb19vL0RvY3VtZW50cy9Ob2lzZVNsZWVwL3NyYy9hcHAvW2xvY2FsZV0vYXVkaW8tdGVzdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp%2F%5Blocale%5D%2Faudio-test%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/[locale]/audio-test/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/[locale]/audio-test/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AudioTestPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_audioData__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/audioData */ \"(ssr)/./src/data/audioData.ts\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(ssr)/./src/hooks/useAudioPlayer.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AudioTestPage() {\n    const [selectedAudio, setSelectedAudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_data_audioData__WEBPACK_IMPORTED_MODULE_2__.audioData[0]);\n    const { isLoading, isPlaying, currentTime, duration, volume, error, play, pause, stop, setVolume } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_3__.useAudioPlayer)();\n    const handlePlay = ()=>{\n        if (selectedAudio) {\n            play(selectedAudio);\n        }\n    };\n    const formatTime = (time)=>{\n        const minutes = Math.floor(time / 60);\n        const seconds = Math.floor(time % 60);\n        return `${minutes}:${seconds.toString().padStart(2, \"0\")}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold mb-8 text-center\",\n                    children: \"音频播放测试\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"选择音频文件\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: _data_audioData__WEBPACK_IMPORTED_MODULE_2__.audioData.map((audio)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedAudio(audio),\n                                    className: `p-4 rounded-lg border-2 text-left transition-colors ${selectedAudio?.id === audio.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium\",\n                                            children: audio.title.zh\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: audio.category\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-gray-500\",\n                                            children: audio.filename\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, audio.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"播放器控制\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        selectedAudio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"当前选择: \",\n                                        selectedAudio.title.zh\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        \"文件路径: /Sounds/\",\n                                        selectedAudio.category.charAt(0).toUpperCase() + selectedAudio.category.slice(1),\n                                        \"/\",\n                                        selectedAudio.filename\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handlePlay,\n                                    disabled: isLoading || !selectedAudio,\n                                    className: \"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300\",\n                                    children: isLoading ? \"加载中...\" : \"播放\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: pause,\n                                    disabled: !isPlaying,\n                                    className: \"px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 disabled:bg-gray-300\",\n                                    children: \"暂停\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: stop,\n                                    className: \"px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600\",\n                                    children: \"停止\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: [\n                                        \"音量: \",\n                                        Math.round(volume * 100),\n                                        \"%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"0\",\n                                    max: \"1\",\n                                    step: \"0.1\",\n                                    value: volume,\n                                    onChange: (e)=>setVolume(parseFloat(e.target.value)),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm text-gray-600 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatTime(currentTime)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatTime(duration)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-500 h-2 rounded-full transition-all\",\n                                        style: {\n                                            width: duration > 0 ? `${currentTime / duration * 100}%` : \"0%\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"状态信息\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"加载状态:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `ml-2 px-2 py-1 rounded ${isLoading ? \"bg-yellow-100 text-yellow-800\" : \"bg-green-100 text-green-800\"}`,\n                                            children: isLoading ? \"加载中\" : \"已就绪\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"播放状态:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `ml-2 px-2 py-1 rounded ${isPlaying ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"}`,\n                                            children: isPlaying ? \"播放中\" : \"已停止\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"当前时间:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: formatTime(currentTime)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"总时长:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: formatTime(duration)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-red-100 border border-red-300 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-red-800 mb-2\",\n                                    children: \"错误信息:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-700\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6 mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"文件系统验证\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mb-2\",\n                                    children: \"实际音频文件列表 (/Sounds/):\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Rain/heavy-rain.mp3, light-rain.mp3, rain-on-car-roof.mp3, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Nature/campfire.mp3, droplets.mp3, river.mp3, waves.mp3, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Noise/brown-noise.wav, pink-noise.wav, white-noise.wav\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Animals/beehive.mp3, birds.mp3, cat-purring.mp3, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Things/boiling-water.mp3, bubbles.mp3, clock.mp3, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Transport/airplane.mp3, train.mp3, sailboat.mp3, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Urban/busy-street.mp3, traffic.mp3, highway.mp3, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Places/airport.mp3, cafe.mp3, church.mp3, etc.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/[locale]/audio-test/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/AudioPlayer.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayer.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayer: () => (/* binding */ AudioPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(ssr)/./src/hooks/useAudioPlayer.ts\");\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./PlayButton */ \"(ssr)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./VolumeControl */ \"(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AudioPlayer auto */ \n\n\n\n\n\n\n\nfunction AudioPlayer({ sound, variant = \"full\", showProgress = true, showVolume = true, showInfo = true, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"common\");\n    const { currentSound, favorites, addToFavorites, removeFromFavorites } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore)();\n    const { play, pause, stop, setVolume, setLoop, seek, isPlaying, isPaused, isLoading, currentTime, duration, volume, isLooping, error } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_2__.useAudioPlayer)();\n    // 使用传入的 sound 或当前播放的 sound\n    const displaySound = sound || currentSound;\n    const isFavorite = displaySound ? favorites.includes(displaySound.id) : false;\n    // 播放控制\n    const handlePlay = ()=>{\n        if (sound && sound.id !== currentSound?.id) {\n            play(sound);\n        } else {\n            play();\n        }\n    };\n    const handlePause = ()=>{\n        pause();\n    };\n    const handleStop = ()=>{\n        stop();\n    };\n    // 收藏切换\n    const toggleFavorite = ()=>{\n        if (!displaySound) return;\n        if (isFavorite) {\n            removeFromFavorites(displaySound.id);\n        } else {\n            addToFavorites(displaySound.id);\n        }\n    };\n    // 循环切换\n    const toggleLoop = ()=>{\n        setLoop(!isLooping);\n    };\n    // 获取音频标题\n    const getAudioTitle = (audioItem, locale = \"en\")=>{\n        return audioItem.title[locale] || audioItem.title.en;\n    };\n    // 获取音频描述\n    const getAudioDescription = (audioItem, locale = \"en\")=>{\n        return audioItem.description?.[locale] || audioItem.description?.en || \"\";\n    };\n    if (!displaySound) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"flex items-center justify-center p-8 text-gray-500 dark:text-gray-400\", \"bg-gray-50 dark:bg-gray-900 rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            fill: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                d: \"M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: t(\"noAudioSelected\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    // 紧凑版本\n    if (variant === \"compact\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_4__.PlayButton, {\n                    isPlaying: isPlaying && currentSound?.id === displaySound.id,\n                    isLoading: isLoading,\n                    onPlay: handlePlay,\n                    onPause: handlePause,\n                    size: \"sm\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                showInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                            children: getAudioTitle(displaySound)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                            children: displaySound.category\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 11\n                }, this),\n                showVolume && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_5__.VolumeControl, {\n                        volume: volume,\n                        onVolumeChange: setVolume,\n                        size: \"sm\",\n                        showIcon: false\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    // 迷你版本\n    if (variant === \"mini\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"flex items-center gap-2\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_4__.PlayButton, {\n                    isPlaying: isPlaying && currentSound?.id === displaySound.id,\n                    isLoading: isLoading,\n                    onPlay: handlePlay,\n                    onPause: handlePause,\n                    size: \"sm\",\n                    variant: \"ghost\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, this),\n                showInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400 truncate\",\n                    children: getAudioTitle(displaySound)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    // 完整版本\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden\", className),\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600 dark:text-red-400\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this),\n            showInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-w-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-1\",\n                                    children: getAudioTitle(displaySound)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                    children: getAudioDescription(displaySound)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-xs text-gray-500 dark:text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full\",\n                                            children: displaySound.category\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        displaySound.tags?.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full\",\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: toggleFavorite,\n                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                            \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: isFavorite ? \"currentColor\" : \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this),\n            showProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_6__.ProgressBar, {\n                    currentTime: currentTime,\n                    duration: duration,\n                    onSeek: seek,\n                    isLoading: isLoading,\n                    showTime: true\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleLoop,\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", isLooping ? \"text-amber-500\" : \"text-gray-400\"),\n                                \"aria-label\": isLooping ? t(\"disableLoop\") : t(\"enableLoop\"),\n                                title: isLooping ? t(\"disableLoop\") : t(\"enableLoop\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M7 7h10v3l4-4-4-4v3H5v6h2V7zm10 10H7v-3l-4 4 4 4v-3h12v-6h-2v4z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleStop,\n                                    disabled: !isPlaying && !isPaused,\n                                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"text-gray-600 dark:text-gray-400\"),\n                                    \"aria-label\": t(\"stop\"),\n                                    title: t(\"stop\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M6 6h12v12H6z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_4__.PlayButton, {\n                                    isPlaying: isPlaying && currentSound?.id === displaySound.id,\n                                    isLoading: isLoading,\n                                    onPlay: handlePlay,\n                                    onPause: handlePause,\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this),\n                        showVolume && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_5__.VolumeControl, {\n                                volume: volume,\n                                onVolumeChange: setVolume,\n                                size: \"md\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9BdWRpb1BsYXllci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2hCO0FBQzRCO0FBQ0w7QUFFVDtBQUNNO0FBQ0o7QUFXckMsU0FBU08sWUFBWSxFQUMxQkMsS0FBSyxFQUNMQyxVQUFVLE1BQU0sRUFDaEJDLGVBQWUsSUFBSSxFQUNuQkMsYUFBYSxJQUFJLEVBQ2pCQyxXQUFXLElBQUksRUFDZkMsU0FBUyxFQUNRO0lBQ2pCLE1BQU1DLElBQUlkLDBEQUFlQSxDQUFDO0lBQzFCLE1BQU0sRUFBRWUsWUFBWSxFQUFFQyxTQUFTLEVBQUVDLGNBQWMsRUFBRUMsbUJBQW1CLEVBQUUsR0FBR2YsZ0VBQWFBO0lBRXRGLE1BQU0sRUFDSmdCLElBQUksRUFDSkMsS0FBSyxFQUNMQyxJQUFJLEVBQ0pDLFNBQVMsRUFDVEMsT0FBTyxFQUNQQyxJQUFJLEVBQ0pDLFNBQVMsRUFDVEMsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLFdBQVcsRUFDWEMsUUFBUSxFQUNSQyxNQUFNLEVBQ05DLFNBQVMsRUFDVEMsS0FBSyxFQUNOLEdBQUc5QixxRUFBY0E7SUFFbEIsMkJBQTJCO0lBQzNCLE1BQU0rQixlQUFlekIsU0FBU087SUFDOUIsTUFBTW1CLGFBQWFELGVBQWVqQixVQUFVbUIsUUFBUSxDQUFDRixhQUFhRyxFQUFFLElBQUk7SUFFeEUsT0FBTztJQUNQLE1BQU1DLGFBQWE7UUFDakIsSUFBSTdCLFNBQVNBLE1BQU00QixFQUFFLEtBQUtyQixjQUFjcUIsSUFBSTtZQUMxQ2pCLEtBQUtYO1FBQ1AsT0FBTztZQUNMVztRQUNGO0lBQ0Y7SUFFQSxNQUFNbUIsY0FBYztRQUNsQmxCO0lBQ0Y7SUFFQSxNQUFNbUIsYUFBYTtRQUNqQmxCO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTW1CLGlCQUFpQjtRQUNyQixJQUFJLENBQUNQLGNBQWM7UUFFbkIsSUFBSUMsWUFBWTtZQUNkaEIsb0JBQW9CZSxhQUFhRyxFQUFFO1FBQ3JDLE9BQU87WUFDTG5CLGVBQWVnQixhQUFhRyxFQUFFO1FBQ2hDO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTUssYUFBYTtRQUNqQmxCLFFBQVEsQ0FBQ1E7SUFDWDtJQUVBLFNBQVM7SUFDVCxNQUFNVyxnQkFBZ0IsQ0FBQ0MsV0FBa0NDLFNBQWlCLElBQUk7UUFDNUUsT0FBT0QsVUFBVUUsS0FBSyxDQUFDRCxPQUF1QyxJQUFJRCxVQUFVRSxLQUFLLENBQUNDLEVBQUU7SUFDdEY7SUFFQSxTQUFTO0lBQ1QsTUFBTUMsc0JBQXNCLENBQUNKLFdBQWtDQyxTQUFpQixJQUFJO1FBQ2xGLE9BQU9ELFVBQVVLLFdBQVcsRUFBRSxDQUFDSixPQUE2QyxJQUFJRCxVQUFVSyxXQUFXLEVBQUVGLE1BQU07SUFDL0c7SUFFQSxJQUFJLENBQUNiLGNBQWM7UUFDakIscUJBQ0UsOERBQUNnQjtZQUFJcEMsV0FBV1osMENBQUlBLENBQ2xCLHlFQUNBLHNHQUNBWTtzQkFFQSw0RUFBQ29DO2dCQUFJcEMsV0FBVTs7a0NBQ2IsOERBQUNvQzt3QkFBSXBDLFdBQVU7a0NBQ2IsNEVBQUNxQzs0QkFBSUMsTUFBSzs0QkFBZUMsU0FBUTtzQ0FDL0IsNEVBQUNDO2dDQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tDQUdaLDhEQUFDQzt3QkFBRTFDLFdBQVU7a0NBQVdDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSWxDO0lBRUEsT0FBTztJQUNQLElBQUlMLFlBQVksV0FBVztRQUN6QixxQkFDRSw4REFBQ3dDO1lBQUlwQyxXQUFXWiwwQ0FBSUEsQ0FDbEIsMEhBQ0FZOzs4QkFFQSw4REFBQ1QsbURBQVVBO29CQUNUcUIsV0FBV0EsYUFBYVYsY0FBY3FCLE9BQU9ILGFBQWFHLEVBQUU7b0JBQzVEVCxXQUFXQTtvQkFDWDZCLFFBQVFuQjtvQkFDUm9CLFNBQVNuQjtvQkFDVG9CLE1BQUs7Ozs7OztnQkFHTjlDLDBCQUNDLDhEQUFDcUM7b0JBQUlwQyxXQUFVOztzQ0FDYiw4REFBQzhDOzRCQUFHOUMsV0FBVTtzQ0FDWDZCLGNBQWNUOzs7Ozs7c0NBRWpCLDhEQUFDc0I7NEJBQUUxQyxXQUFVO3NDQUNWb0IsYUFBYTJCLFFBQVE7Ozs7Ozs7Ozs7OztnQkFLM0JqRCw0QkFDQyw4REFBQ3NDO29CQUFJcEMsV0FBVTs4QkFDYiw0RUFBQ1IseURBQWFBO3dCQUNaeUIsUUFBUUE7d0JBQ1IrQixnQkFBZ0J2Qzt3QkFDaEJvQyxNQUFLO3dCQUNMSSxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztJQU10QjtJQUVBLE9BQU87SUFDUCxJQUFJckQsWUFBWSxRQUFRO1FBQ3RCLHFCQUNFLDhEQUFDd0M7WUFBSXBDLFdBQVdaLDBDQUFJQSxDQUFDLDJCQUEyQlk7OzhCQUM5Qyw4REFBQ1QsbURBQVVBO29CQUNUcUIsV0FBV0EsYUFBYVYsY0FBY3FCLE9BQU9ILGFBQWFHLEVBQUU7b0JBQzVEVCxXQUFXQTtvQkFDWDZCLFFBQVFuQjtvQkFDUm9CLFNBQVNuQjtvQkFDVG9CLE1BQUs7b0JBQ0xqRCxTQUFROzs7Ozs7Z0JBRVRHLDBCQUNDLDhEQUFDbUQ7b0JBQUtsRCxXQUFVOzhCQUNiNkIsY0FBY1Q7Ozs7Ozs7Ozs7OztJQUt6QjtJQUVBLE9BQU87SUFDUCxxQkFDRSw4REFBQ2dCO1FBQUlwQyxXQUFXWiwwQ0FBSUEsQ0FDbEIsOEdBQ0FZOztZQUdDbUIsdUJBQ0MsOERBQUNpQjtnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUMwQztvQkFBRTFDLFdBQVU7OEJBQTBDbUI7Ozs7Ozs7Ozs7O1lBSzFEcEIsMEJBQ0MsOERBQUNxQztnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUNvQztvQkFBSXBDLFdBQVU7O3NDQUNiLDhEQUFDb0M7NEJBQUlwQyxXQUFVOzs4Q0FDYiw4REFBQ21EO29DQUFHbkQsV0FBVTs4Q0FDWDZCLGNBQWNUOzs7Ozs7OENBRWpCLDhEQUFDc0I7b0NBQUUxQyxXQUFVOzhDQUNWa0Msb0JBQW9CZDs7Ozs7OzhDQUV2Qiw4REFBQ2dCO29DQUFJcEMsV0FBVTs7c0RBQ2IsOERBQUNrRDs0Q0FBS2xELFdBQVU7c0RBQ2JvQixhQUFhMkIsUUFBUTs7Ozs7O3dDQUV2QjNCLGFBQWFnQyxJQUFJLEVBQUVDLElBQUksQ0FBQ0Msb0JBQ3ZCLDhEQUFDSjtnREFBZWxELFdBQVU7MERBQ3ZCc0Q7K0NBRFFBOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRakIsOERBQUNDOzRCQUNDQyxTQUFTN0I7NEJBQ1QzQixXQUFXWiwwQ0FBSUEsQ0FDYixzQ0FDQSw0Q0FDQSx3REFDQWlDLGFBQWEsaUJBQWlCOzRCQUVoQ29DLGNBQVlwQyxhQUFhcEIsRUFBRSx5QkFBeUJBLEVBQUU7c0NBRXRELDRFQUFDb0M7Z0NBQUlyQyxXQUFVO2dDQUFVc0MsTUFBTWpCLGFBQWEsaUJBQWlCO2dDQUFRcUMsUUFBTztnQ0FBZW5CLFNBQVE7MENBQ2pHLDRFQUFDQztvQ0FBS21CLGVBQWM7b0NBQVFDLGdCQUFlO29DQUFRQyxhQUFhO29DQUFHcEIsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUTlFNUMsOEJBQ0MsOERBQUN1QztnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUNQLHFEQUFXQTtvQkFDVnNCLGFBQWFBO29CQUNiQyxVQUFVQTtvQkFDVjhDLFFBQVFuRDtvQkFDUkcsV0FBV0E7b0JBQ1hpRCxVQUFVOzs7Ozs7Ozs7OzswQkFNaEIsOERBQUMzQjtnQkFBSXBDLFdBQVU7MEJBQ2IsNEVBQUNvQztvQkFBSXBDLFdBQVU7O3NDQUViLDhEQUFDb0M7NEJBQUlwQyxXQUFVO3NDQUNiLDRFQUFDdUQ7Z0NBQ0NDLFNBQVM1QjtnQ0FDVDVCLFdBQVdaLDBDQUFJQSxDQUNiLHNDQUNBLDRDQUNBLHdEQUNBOEIsWUFBWSxtQkFBbUI7Z0NBRWpDdUMsY0FBWXZDLFlBQVlqQixFQUFFLGlCQUFpQkEsRUFBRTtnQ0FDN0MrQixPQUFPZCxZQUFZakIsRUFBRSxpQkFBaUJBLEVBQUU7MENBRXhDLDRFQUFDb0M7b0NBQUlyQyxXQUFVO29DQUFVc0MsTUFBSztvQ0FBZUMsU0FBUTs4Q0FDbkQsNEVBQUNDO3dDQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTWQsOERBQUNMOzRCQUFJcEMsV0FBVTs7OENBQ2IsOERBQUN1RDtvQ0FDQ0MsU0FBUzlCO29DQUNUc0MsVUFBVSxDQUFDcEQsYUFBYSxDQUFDQztvQ0FDekJiLFdBQVdaLDBDQUFJQSxDQUNiLHNDQUNBLDRDQUNBLHdEQUNBLG1EQUNBO29DQUVGcUUsY0FBWXhELEVBQUU7b0NBQ2QrQixPQUFPL0IsRUFBRTs4Q0FFVCw0RUFBQ29DO3dDQUFJckMsV0FBVTt3Q0FBVXNDLE1BQUs7d0NBQWVDLFNBQVE7a0RBQ25ELDRFQUFDQzs0Q0FBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FJWiw4REFBQ2xELG1EQUFVQTtvQ0FDVHFCLFdBQVdBLGFBQWFWLGNBQWNxQixPQUFPSCxhQUFhRyxFQUFFO29DQUM1RFQsV0FBV0E7b0NBQ1g2QixRQUFRbkI7b0NBQ1JvQixTQUFTbkI7b0NBQ1RvQixNQUFLOzs7Ozs7Ozs7Ozs7d0JBS1IvQyw0QkFDQyw4REFBQ3NDOzRCQUFJcEMsV0FBVTtzQ0FDYiw0RUFBQ1IseURBQWFBO2dDQUNaeUIsUUFBUUE7Z0NBQ1IrQixnQkFBZ0J2QztnQ0FDaEJvQyxNQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUXJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9BdWRpb1BsYXllci50c3g/NzYyYiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gJ25leHQtaW50bCc7XG5pbXBvcnQgeyBjbHN4IH0gZnJvbSAnY2xzeCc7XG5pbXBvcnQgeyB1c2VBdWRpb1BsYXllciB9IGZyb20gJ0AvaG9va3MvdXNlQXVkaW9QbGF5ZXInO1xuaW1wb3J0IHsgdXNlQXVkaW9TdG9yZSB9IGZyb20gJ0Avc3RvcmUvYXVkaW9TdG9yZSc7XG5pbXBvcnQgeyBNdWx0aWxpbmd1YWxBdWRpb0l0ZW0gfSBmcm9tICdAL3R5cGVzL2F1ZGlvJztcbmltcG9ydCB7IFBsYXlCdXR0b24gfSBmcm9tICcuL1BsYXlCdXR0b24nO1xuaW1wb3J0IHsgVm9sdW1lQ29udHJvbCB9IGZyb20gJy4vVm9sdW1lQ29udHJvbCc7XG5pbXBvcnQgeyBQcm9ncmVzc0JhciB9IGZyb20gJy4vUHJvZ3Jlc3NCYXInO1xuXG5pbnRlcmZhY2UgQXVkaW9QbGF5ZXJQcm9wcyB7XG4gIHNvdW5kPzogTXVsdGlsaW5ndWFsQXVkaW9JdGVtO1xuICB2YXJpYW50PzogJ2NvbXBhY3QnIHwgJ2Z1bGwnIHwgJ21pbmknO1xuICBzaG93UHJvZ3Jlc3M/OiBib29sZWFuO1xuICBzaG93Vm9sdW1lPzogYm9vbGVhbjtcbiAgc2hvd0luZm8/OiBib29sZWFuO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdWRpb1BsYXllcih7XG4gIHNvdW5kLFxuICB2YXJpYW50ID0gJ2Z1bGwnLFxuICBzaG93UHJvZ3Jlc3MgPSB0cnVlLFxuICBzaG93Vm9sdW1lID0gdHJ1ZSxcbiAgc2hvd0luZm8gPSB0cnVlLFxuICBjbGFzc05hbWUsXG59OiBBdWRpb1BsYXllclByb3BzKSB7XG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoJ2NvbW1vbicpO1xuICBjb25zdCB7IGN1cnJlbnRTb3VuZCwgZmF2b3JpdGVzLCBhZGRUb0Zhdm9yaXRlcywgcmVtb3ZlRnJvbUZhdm9yaXRlcyB9ID0gdXNlQXVkaW9TdG9yZSgpO1xuICBcbiAgY29uc3Qge1xuICAgIHBsYXksXG4gICAgcGF1c2UsXG4gICAgc3RvcCxcbiAgICBzZXRWb2x1bWUsXG4gICAgc2V0TG9vcCxcbiAgICBzZWVrLFxuICAgIGlzUGxheWluZyxcbiAgICBpc1BhdXNlZCxcbiAgICBpc0xvYWRpbmcsXG4gICAgY3VycmVudFRpbWUsXG4gICAgZHVyYXRpb24sXG4gICAgdm9sdW1lLFxuICAgIGlzTG9vcGluZyxcbiAgICBlcnJvcixcbiAgfSA9IHVzZUF1ZGlvUGxheWVyKCk7XG5cbiAgLy8g5L2/55So5Lyg5YWl55qEIHNvdW5kIOaIluW9k+WJjeaSreaUvueahCBzb3VuZFxuICBjb25zdCBkaXNwbGF5U291bmQgPSBzb3VuZCB8fCBjdXJyZW50U291bmQ7XG4gIGNvbnN0IGlzRmF2b3JpdGUgPSBkaXNwbGF5U291bmQgPyBmYXZvcml0ZXMuaW5jbHVkZXMoZGlzcGxheVNvdW5kLmlkKSA6IGZhbHNlO1xuXG4gIC8vIOaSreaUvuaOp+WItlxuICBjb25zdCBoYW5kbGVQbGF5ID0gKCkgPT4ge1xuICAgIGlmIChzb3VuZCAmJiBzb3VuZC5pZCAhPT0gY3VycmVudFNvdW5kPy5pZCkge1xuICAgICAgcGxheShzb3VuZCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHBsYXkoKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlUGF1c2UgPSAoKSA9PiB7XG4gICAgcGF1c2UoKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdG9wID0gKCkgPT4ge1xuICAgIHN0b3AoKTtcbiAgfTtcblxuICAvLyDmlLbol4/liIfmjaJcbiAgY29uc3QgdG9nZ2xlRmF2b3JpdGUgPSAoKSA9PiB7XG4gICAgaWYgKCFkaXNwbGF5U291bmQpIHJldHVybjtcbiAgICBcbiAgICBpZiAoaXNGYXZvcml0ZSkge1xuICAgICAgcmVtb3ZlRnJvbUZhdm9yaXRlcyhkaXNwbGF5U291bmQuaWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICBhZGRUb0Zhdm9yaXRlcyhkaXNwbGF5U291bmQuaWQpO1xuICAgIH1cbiAgfTtcblxuICAvLyDlvqrnjq/liIfmjaJcbiAgY29uc3QgdG9nZ2xlTG9vcCA9ICgpID0+IHtcbiAgICBzZXRMb29wKCFpc0xvb3BpbmcpO1xuICB9O1xuXG4gIC8vIOiOt+WPlumfs+mikeagh+mimFxuICBjb25zdCBnZXRBdWRpb1RpdGxlID0gKGF1ZGlvSXRlbTogTXVsdGlsaW5ndWFsQXVkaW9JdGVtLCBsb2NhbGU6IHN0cmluZyA9ICdlbicpID0+IHtcbiAgICByZXR1cm4gYXVkaW9JdGVtLnRpdGxlW2xvY2FsZSBhcyBrZXlvZiB0eXBlb2YgYXVkaW9JdGVtLnRpdGxlXSB8fCBhdWRpb0l0ZW0udGl0bGUuZW47XG4gIH07XG5cbiAgLy8g6I635Y+W6Z+z6aKR5o+P6L+wXG4gIGNvbnN0IGdldEF1ZGlvRGVzY3JpcHRpb24gPSAoYXVkaW9JdGVtOiBNdWx0aWxpbmd1YWxBdWRpb0l0ZW0sIGxvY2FsZTogc3RyaW5nID0gJ2VuJykgPT4ge1xuICAgIHJldHVybiBhdWRpb0l0ZW0uZGVzY3JpcHRpb24/Lltsb2NhbGUgYXMga2V5b2YgdHlwZW9mIGF1ZGlvSXRlbS5kZXNjcmlwdGlvbl0gfHwgYXVkaW9JdGVtLmRlc2NyaXB0aW9uPy5lbiB8fCAnJztcbiAgfTtcblxuICBpZiAoIWRpc3BsYXlTb3VuZCkge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzeChcbiAgICAgICAgJ2ZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtOCB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMCcsXG4gICAgICAgICdiZy1ncmF5LTUwIGRhcms6YmctZ3JheS05MDAgcm91bmRlZC1sZyBib3JkZXItMiBib3JkZXItZGFzaGVkIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCcsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTIgaC0xMiBteC1hdXRvIG1iLTQgdGV4dC1ncmF5LTMwMCBkYXJrOnRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgIDxzdmcgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgPHBhdGggZD1cIk0xMiAzdjEwLjU1Yy0uNTktLjM0LTEuMjctLjU1LTItLjU1LTIuMjEgMC00IDEuNzktNCA0czEuNzkgNCA0IDQgNC0xLjc5IDQtNFY3aDRWM2gtNnpcIi8+XG4gICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3QoJ25vQXVkaW9TZWxlY3RlZCcpfTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgLy8g57Sn5YeR54mI5pysXG4gIGlmICh2YXJpYW50ID09PSAnY29tcGFjdCcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgICdmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBwLTMgYmctd2hpdGUgZGFyazpiZy1ncmF5LTgwMCByb3VuZGVkLWxnIHNoYWRvdy1zbSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIGRhcms6Ym9yZGVyLWdyYXktNzAwJyxcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfT5cbiAgICAgICAgPFBsYXlCdXR0b25cbiAgICAgICAgICBpc1BsYXlpbmc9e2lzUGxheWluZyAmJiBjdXJyZW50U291bmQ/LmlkID09PSBkaXNwbGF5U291bmQuaWR9XG4gICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmd9XG4gICAgICAgICAgb25QbGF5PXtoYW5kbGVQbGF5fVxuICAgICAgICAgIG9uUGF1c2U9e2hhbmRsZVBhdXNlfVxuICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgIC8+XG4gICAgICAgIFxuICAgICAgICB7c2hvd0luZm8gJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgIHtnZXRBdWRpb1RpdGxlKGRpc3BsYXlTb3VuZCl9XG4gICAgICAgICAgICA8L2g0PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTQwMCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICB7ZGlzcGxheVNvdW5kLmNhdGVnb3J5fVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHtzaG93Vm9sdW1lICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjBcIj5cbiAgICAgICAgICAgIDxWb2x1bWVDb250cm9sXG4gICAgICAgICAgICAgIHZvbHVtZT17dm9sdW1lfVxuICAgICAgICAgICAgICBvblZvbHVtZUNoYW5nZT17c2V0Vm9sdW1lfVxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBzaG93SWNvbj17ZmFsc2V9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIOi/t+S9oOeJiOacrFxuICBpZiAodmFyaWFudCA9PT0gJ21pbmknKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbHN4KCdmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMicsIGNsYXNzTmFtZSl9PlxuICAgICAgICA8UGxheUJ1dHRvblxuICAgICAgICAgIGlzUGxheWluZz17aXNQbGF5aW5nICYmIGN1cnJlbnRTb3VuZD8uaWQgPT09IGRpc3BsYXlTb3VuZC5pZH1cbiAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgICBvblBsYXk9e2hhbmRsZVBsYXl9XG4gICAgICAgICAgb25QYXVzZT17aGFuZGxlUGF1c2V9XG4gICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAvPlxuICAgICAgICB7c2hvd0luZm8gJiYgKFxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgIHtnZXRBdWRpb1RpdGxlKGRpc3BsYXlTb3VuZCl9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIOWujOaVtOeJiOacrFxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgJ2JnLXdoaXRlIGRhcms6YmctZ3JheS04MDAgcm91bmRlZC14bCBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBkYXJrOmJvcmRlci1ncmF5LTcwMCBvdmVyZmxvdy1oaWRkZW4nLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX0+XG4gICAgICB7Lyog6ZSZ6K+v5o+Q56S6ICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctcmVkLTUwIGRhcms6YmctcmVkLTkwMC8yMCBib3JkZXItYiBib3JkZXItcmVkLTIwMCBkYXJrOmJvcmRlci1yZWQtODAwXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDAgZGFyazp0ZXh0LXJlZC00MDBcIj57ZXJyb3J9PC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiDpn7PpopHkv6Hmga8gKi99XG4gICAgICB7c2hvd0luZm8gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBwYi00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LWdyYXktMTAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICB7Z2V0QXVkaW9UaXRsZShkaXNwbGF5U291bmQpfVxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICB7Z2V0QXVkaW9EZXNjcmlwdGlvbihkaXNwbGF5U291bmQpfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgdGV4dC14cyB0ZXh0LWdyYXktNTAwIGRhcms6dGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTEwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAge2Rpc3BsYXlTb3VuZC5jYXRlZ29yeX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAge2Rpc3BsYXlTb3VuZC50YWdzPy5tYXAoKHRhZykgPT4gKFxuICAgICAgICAgICAgICAgICAgPHNwYW4ga2V5PXt0YWd9IGNsYXNzTmFtZT1cInB4LTIgcHktMSBiZy1ncmF5LTEwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICB7dGFnfVxuICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICB7Lyog5pS26JeP5oyJ6ZKuICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVGYXZvcml0ZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgICAgICAgICAgICdwLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tY29sb3JzJyxcbiAgICAgICAgICAgICAgICAnaG92ZXI6YmctZ3JheS0xMDAgZGFyazpob3ZlcjpiZy1ncmF5LTcwMCcsXG4gICAgICAgICAgICAgICAgJ2ZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hbWJlci01MDAnLFxuICAgICAgICAgICAgICAgIGlzRmF2b3JpdGUgPyAndGV4dC1yZWQtNTAwJyA6ICd0ZXh0LWdyYXktNDAwJ1xuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICBhcmlhLWxhYmVsPXtpc0Zhdm9yaXRlID8gdCgncmVtb3ZlRnJvbUZhdm9yaXRlcycpIDogdCgnYWRkVG9GYXZvcml0ZXMnKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD17aXNGYXZvcml0ZSA/ICdjdXJyZW50Q29sb3InIDogJ25vbmUnfSBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQuMzE4IDYuMzE4YTQuNSA0LjUgMCAwMDAgNi4zNjRMMTIgMjAuMzY0bDcuNjgyLTcuNjgyYTQuNSA0LjUgMCAwMC02LjM2NC02LjM2NEwxMiA3LjYzNmwtMS4zMTgtMS4zMThhNC41IDQuNSAwIDAwLTYuMzY0IDB6XCIgLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog6L+b5bqm5p2hICovfVxuICAgICAge3Nob3dQcm9ncmVzcyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBwYi00XCI+XG4gICAgICAgICAgPFByb2dyZXNzQmFyXG4gICAgICAgICAgICBjdXJyZW50VGltZT17Y3VycmVudFRpbWV9XG4gICAgICAgICAgICBkdXJhdGlvbj17ZHVyYXRpb259XG4gICAgICAgICAgICBvblNlZWs9e3NlZWt9XG4gICAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgICAgIHNob3dUaW1lPXt0cnVlfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIOaOp+WItumdouadvyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBwYi02XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgey8qIOW3puS+p+aOp+WItiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e3RvZ2dsZUxvb3B9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17Y2xzeChcbiAgICAgICAgICAgICAgICAncC0yIHJvdW5kZWQtZnVsbCB0cmFuc2l0aW9uLWNvbG9ycycsXG4gICAgICAgICAgICAgICAgJ2hvdmVyOmJnLWdyYXktMTAwIGRhcms6aG92ZXI6YmctZ3JheS03MDAnLFxuICAgICAgICAgICAgICAgICdmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYW1iZXItNTAwJyxcbiAgICAgICAgICAgICAgICBpc0xvb3BpbmcgPyAndGV4dC1hbWJlci01MDAnIDogJ3RleHQtZ3JheS00MDAnXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9e2lzTG9vcGluZyA/IHQoJ2Rpc2FibGVMb29wJykgOiB0KCdlbmFibGVMb29wJyl9XG4gICAgICAgICAgICAgIHRpdGxlPXtpc0xvb3BpbmcgPyB0KCdkaXNhYmxlTG9vcCcpIDogdCgnZW5hYmxlTG9vcCcpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgIDxwYXRoIGQ9XCJNNyA3aDEwdjNsNC00LTQtNHYzSDV2NmgyVjd6bTEwIDEwSDd2LTNsLTQgNCA0IDR2LTNoMTJ2LTZoLTJ2NHpcIi8+XG4gICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Lit5aSu5pKt5pS+5o6n5Yi2ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3RvcH1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFpc1BsYXlpbmcgJiYgIWlzUGF1c2VkfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgICAgICAgICAgJ3AtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1jb2xvcnMnLFxuICAgICAgICAgICAgICAgICdob3ZlcjpiZy1ncmF5LTEwMCBkYXJrOmhvdmVyOmJnLWdyYXktNzAwJyxcbiAgICAgICAgICAgICAgICAnZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWFtYmVyLTUwMCcsXG4gICAgICAgICAgICAgICAgJ2Rpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkJyxcbiAgICAgICAgICAgICAgICAndGV4dC1ncmF5LTYwMCBkYXJrOnRleHQtZ3JheS00MDAnXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9e3QoJ3N0b3AnKX1cbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ3N0b3AnKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBkPVwiTTYgNmgxMnYxMkg2elwiLz5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgPFBsYXlCdXR0b25cbiAgICAgICAgICAgICAgaXNQbGF5aW5nPXtpc1BsYXlpbmcgJiYgY3VycmVudFNvdW5kPy5pZCA9PT0gZGlzcGxheVNvdW5kLmlkfVxuICAgICAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cbiAgICAgICAgICAgICAgb25QbGF5PXtoYW5kbGVQbGF5fVxuICAgICAgICAgICAgICBvblBhdXNlPXtoYW5kbGVQYXVzZX1cbiAgICAgICAgICAgICAgc2l6ZT1cImxnXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog5Y+z5L6n6Z+z6YeP5o6n5Yi2ICovfVxuICAgICAgICAgIHtzaG93Vm9sdW1lICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0zMlwiPlxuICAgICAgICAgICAgICA8Vm9sdW1lQ29udHJvbFxuICAgICAgICAgICAgICAgIHZvbHVtZT17dm9sdW1lfVxuICAgICAgICAgICAgICAgIG9uVm9sdW1lQ2hhbmdlPXtzZXRWb2x1bWV9XG4gICAgICAgICAgICAgICAgc2l6ZT1cIm1kXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlVHJhbnNsYXRpb25zIiwiY2xzeCIsInVzZUF1ZGlvUGxheWVyIiwidXNlQXVkaW9TdG9yZSIsIlBsYXlCdXR0b24iLCJWb2x1bWVDb250cm9sIiwiUHJvZ3Jlc3NCYXIiLCJBdWRpb1BsYXllciIsInNvdW5kIiwidmFyaWFudCIsInNob3dQcm9ncmVzcyIsInNob3dWb2x1bWUiLCJzaG93SW5mbyIsImNsYXNzTmFtZSIsInQiLCJjdXJyZW50U291bmQiLCJmYXZvcml0ZXMiLCJhZGRUb0Zhdm9yaXRlcyIsInJlbW92ZUZyb21GYXZvcml0ZXMiLCJwbGF5IiwicGF1c2UiLCJzdG9wIiwic2V0Vm9sdW1lIiwic2V0TG9vcCIsInNlZWsiLCJpc1BsYXlpbmciLCJpc1BhdXNlZCIsImlzTG9hZGluZyIsImN1cnJlbnRUaW1lIiwiZHVyYXRpb24iLCJ2b2x1bWUiLCJpc0xvb3BpbmciLCJlcnJvciIsImRpc3BsYXlTb3VuZCIsImlzRmF2b3JpdGUiLCJpbmNsdWRlcyIsImlkIiwiaGFuZGxlUGxheSIsImhhbmRsZVBhdXNlIiwiaGFuZGxlU3RvcCIsInRvZ2dsZUZhdm9yaXRlIiwidG9nZ2xlTG9vcCIsImdldEF1ZGlvVGl0bGUiLCJhdWRpb0l0ZW0iLCJsb2NhbGUiLCJ0aXRsZSIsImVuIiwiZ2V0QXVkaW9EZXNjcmlwdGlvbiIsImRlc2NyaXB0aW9uIiwiZGl2Iiwic3ZnIiwiZmlsbCIsInZpZXdCb3giLCJwYXRoIiwiZCIsInAiLCJvblBsYXkiLCJvblBhdXNlIiwic2l6ZSIsImg0IiwiY2F0ZWdvcnkiLCJvblZvbHVtZUNoYW5nZSIsInNob3dJY29uIiwic3BhbiIsImgzIiwidGFncyIsIm1hcCIsInRhZyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwic3Ryb2tlIiwic3Ryb2tlTGluZWNhcCIsInN0cm9rZUxpbmVqb2luIiwic3Ryb2tlV2lkdGgiLCJvblNlZWsiLCJzaG93VGltZSIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/AudioPlayer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/AudioPlayerProvider.tsx":
/*!************************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayerProvider.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayerProvider: () => (/* binding */ AudioPlayerProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n/* harmony import */ var _StandardPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StandardPlayer */ \"(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx\");\n/* __next_internal_client_entry_do_not_use__ AudioPlayerProvider auto */ \n\n\n\nfunction AudioPlayerProvider({ children, className }) {\n    const { currentSound, playerUI, setPlayerVisible } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore)();\n    // 监听音频播放状态，自动显示/隐藏播放器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentSound && !playerUI.isVisible) {\n            setPlayerVisible(true);\n        }\n    }, [\n        currentSound,\n        playerUI.isVisible,\n        setPlayerVisible\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            children,\n            playerUI.mode === \"standard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StandardPlayer__WEBPACK_IMPORTED_MODULE_3__.StandardPlayer, {\n                position: playerUI.position,\n                showMixingButton: true,\n                showSleepModeButton: true,\n                autoHide: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this),\n            playerUI.mode === \"sleep\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-gray-900 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"睡眠模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 mb-8\",\n                            children: \"睡眠模式界面将在第三阶段实现\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore.getState().setPlayerMode(\"standard\"),\n                            className: \"px-6 py-3 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors\",\n                            children: \"返回标准模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/PlayButton.tsx":
/*!***************************************************!*\
  !*** ./src/components/AudioPlayer/PlayButton.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GhostPlayButton: () => (/* binding */ GhostPlayButton),\n/* harmony export */   LargePlayButton: () => (/* binding */ LargePlayButton),\n/* harmony export */   PlayButton: () => (/* binding */ PlayButton),\n/* harmony export */   PrimaryPlayButton: () => (/* binding */ PrimaryPlayButton),\n/* harmony export */   SecondaryPlayButton: () => (/* binding */ SecondaryPlayButton),\n/* harmony export */   SmallPlayButton: () => (/* binding */ SmallPlayButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ PlayButton,PrimaryPlayButton,SecondaryPlayButton,GhostPlayButton,LargePlayButton,SmallPlayButton auto */ \n\n\nfunction PlayButton({ isPlaying, isLoading, onPlay, onPause, size = \"md\", variant = \"primary\", disabled = false, className }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"common\");\n    const handleClick = ()=>{\n        if (disabled || isLoading) return;\n        if (isPlaying) {\n            onPause();\n        } else {\n            onPlay();\n        }\n    };\n    const sizeClasses = {\n        sm: \"w-8 h-8 p-1.5\",\n        md: \"w-12 h-12 p-3\",\n        lg: \"w-16 h-16 p-4\"\n    };\n    const variantClasses = {\n        primary: \"bg-amber-500 hover:bg-amber-600 text-white shadow-lg hover:shadow-xl\",\n        secondary: \"bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200\",\n        ghost: \"bg-transparent hover:bg-gray-100 text-gray-600 dark:hover:bg-gray-800 dark:text-gray-400\"\n    };\n    const iconSize = {\n        sm: \"w-3 h-3\",\n        md: \"w-4 h-4\",\n        lg: \"w-6 h-6\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClick,\n        disabled: disabled || isLoading,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(// 基础样式\n        \"relative rounded-full transition-all duration-200 ease-in-out\", \"focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"transform hover:scale-105 active:scale-95\", // 尺寸样式\n        sizeClasses[size], // 变体样式\n        variantClasses[variant], // 自定义类名\n        className),\n        \"aria-label\": isPlaying ? t(\"pause\") : t(\"play\"),\n        title: isPlaying ? t(\"pause\") : t(\"play\"),\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"animate-spin rounded-full border-2 border-current border-t-transparent\", iconSize[size])\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this),\n            !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: isPlaying ? // 暂停图标\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: iconSize[size],\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M6 4h4v16H6V4zm8 0h4v16h-4V4z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 13\n                }, this) : // 播放图标\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(iconSize[size], \"ml-0.5\"),\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8 5v14l11-7z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this),\n            isPlaying && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-full animate-ping bg-current opacity-20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n// 预设的播放按钮变体\nfunction PrimaryPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"primary\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 127,\n        columnNumber: 10\n    }, this);\n}\nfunction SecondaryPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"secondary\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 131,\n        columnNumber: 10\n    }, this);\n}\nfunction GhostPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"ghost\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 135,\n        columnNumber: 10\n    }, this);\n}\n// 大型播放按钮（用于主播放器）\nfunction LargePlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        size: \"lg\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 140,\n        columnNumber: 10\n    }, this);\n}\n// 小型播放按钮（用于列表项）\nfunction SmallPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        size: \"sm\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 145,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/PlayButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/ProgressBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/ProgressBar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProgressBar: () => (/* binding */ ProgressBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ ProgressBar auto */ \n\n\nfunction ProgressBar({ currentTime, duration, onSeek, isLoading = false, showTime = true, size = \"md\", className, disabled = false }) {\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragTime, setDragTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [tooltipTime, setTooltipTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const progressRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 格式化时间显示\n    const formatTime = (seconds)=>{\n        if (!isFinite(seconds) || seconds < 0) return \"0:00\";\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    // 计算进度百分比\n    const progress = duration > 0 ? currentTime / duration * 100 : 0;\n    const displayTime = isDragging ? dragTime : currentTime;\n    const displayProgress = isDragging ? dragTime / duration * 100 : progress;\n    // 处理拖拽开始\n    const handlePointerDown = (event)=>{\n        if (disabled || duration === 0) return;\n        setIsDragging(true);\n        updateTimeFromEvent(event);\n        event.preventDefault();\n    };\n    // 处理鼠标移动（用于显示预览时间）\n    const handleMouseMove = (event)=>{\n        if (!progressRef.current || duration === 0) return;\n        const rect = progressRef.current.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const percentage = Math.max(0, Math.min(1, x / rect.width));\n        const time = percentage * duration;\n        setTooltipTime(time);\n        setShowTooltip(true);\n    };\n    // 处理鼠标离开\n    const handleMouseLeave = ()=>{\n        if (!isDragging) {\n            setShowTooltip(false);\n        }\n    };\n    // 从事件更新时间\n    const updateTimeFromEvent = (event)=>{\n        if (!progressRef.current || duration === 0) return;\n        const rect = progressRef.current.getBoundingClientRect();\n        const x = event.clientX - rect.left;\n        const percentage = Math.max(0, Math.min(1, x / rect.width));\n        const time = percentage * duration;\n        setDragTime(time);\n        setTooltipTime(time);\n    };\n    // 处理拖拽移动\n    const handlePointerMove = (event)=>{\n        if (!isDragging || disabled) return;\n        updateTimeFromEvent(event);\n    };\n    // 处理拖拽结束\n    const handlePointerUp = ()=>{\n        if (isDragging) {\n            onSeek(dragTime);\n            setIsDragging(false);\n            setShowTooltip(false);\n        }\n    };\n    // 处理点击跳转\n    const handleClick = (event)=>{\n        if (disabled || duration === 0 || isDragging) return;\n        const rect = progressRef.current?.getBoundingClientRect();\n        if (!rect) return;\n        const x = event.clientX - rect.left;\n        const percentage = Math.max(0, Math.min(1, x / rect.width));\n        const time = percentage * duration;\n        onSeek(time);\n    };\n    // 键盘控制\n    const handleKeyDown = (event)=>{\n        if (disabled || duration === 0) return;\n        let newTime = currentTime;\n        const step = duration * 0.05; // 5% 步长\n        switch(event.key){\n            case \"ArrowLeft\":\n                newTime = Math.max(0, currentTime - step);\n                break;\n            case \"ArrowRight\":\n                newTime = Math.min(duration, currentTime + step);\n                break;\n            case \"Home\":\n                newTime = 0;\n                break;\n            case \"End\":\n                newTime = duration;\n                break;\n            default:\n                return;\n        }\n        event.preventDefault();\n        onSeek(newTime);\n    };\n    // 监听全局鼠标事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"pointermove\", handlePointerMove);\n            document.addEventListener(\"pointerup\", handlePointerUp);\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp);\n            };\n        }\n        return undefined;\n    }, [\n        isDragging,\n        dragTime\n    ]);\n    const sizeClasses = {\n        sm: \"h-1\",\n        md: \"h-2\",\n        lg: \"h-3\"\n    };\n    const thumbSize = {\n        sm: \"w-3 h-3\",\n        md: \"w-4 h-4\",\n        lg: \"w-5 h-5\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"w-full\", className),\n        children: [\n            showTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-2 text-sm text-gray-600 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-mono\",\n                        children: formatTime(displayTime)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-mono\",\n                        children: formatTime(duration)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: progressRef,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer group\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", sizeClasses[size], disabled && \"opacity-50 cursor-not-allowed\"),\n                        onPointerDown: handlePointerDown,\n                        onClick: handleClick,\n                        onMouseMove: handleMouseMove,\n                        onMouseLeave: handleMouseLeave,\n                        onKeyDown: handleKeyDown,\n                        tabIndex: disabled ? -1 : 0,\n                        role: \"slider\",\n                        \"aria-valuemin\": 0,\n                        \"aria-valuemax\": duration,\n                        \"aria-valuenow\": currentTime,\n                        \"aria-label\": \"音频进度\",\n                        children: [\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gray-300 dark:bg-gray-600 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute left-0 top-0 bg-amber-500 rounded-full transition-all duration-150\", sizeClasses[size]),\n                                style: {\n                                    width: `${Math.max(0, Math.min(100, displayProgress))}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute top-1/2 bg-white border-2 border-amber-500 rounded-full shadow-md\", \"transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150\", \"opacity-0 group-hover:opacity-100\", isDragging && \"opacity-100 scale-125\", thumbSize[size]),\n                                style: {\n                                    left: `${Math.max(0, Math.min(100, displayProgress))}%`\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    showTooltip && duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg transform -translate-x-1/2 -top-8\",\n                        style: {\n                            left: `${Math.max(0, Math.min(100, tooltipTime / duration * 100))}%`\n                        },\n                        children: formatTime(tooltipTime)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            duration > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-1 text-xs text-gray-500 dark:text-gray-500 text-center\",\n                children: [\n                    Math.round(displayProgress),\n                    \"%\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9Qcm9ncmVzc0Jhci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVvRDtBQUN4QjtBQWFyQixTQUFTSSxZQUFZLEVBQzFCQyxXQUFXLEVBQ1hDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxZQUFZLEtBQUssRUFDakJDLFdBQVcsSUFBSSxFQUNmQyxPQUFPLElBQUksRUFDWEMsU0FBUyxFQUNUQyxXQUFXLEtBQUssRUFDQztJQUNqQixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR2QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZSxVQUFVQyxZQUFZLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNpQixhQUFhQyxlQUFlLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNtQixhQUFhQyxlQUFlLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNcUIsY0FBY3BCLDZDQUFNQSxDQUFpQjtJQUUzQyxVQUFVO0lBQ1YsTUFBTXFCLGFBQWEsQ0FBQ0M7UUFDbEIsSUFBSSxDQUFDQyxTQUFTRCxZQUFZQSxVQUFVLEdBQUcsT0FBTztRQUU5QyxNQUFNRSxPQUFPQyxLQUFLQyxLQUFLLENBQUNKLFVBQVU7UUFDbEMsTUFBTUssT0FBT0YsS0FBS0MsS0FBSyxDQUFDSixVQUFVO1FBQ2xDLE9BQU8sQ0FBQyxFQUFFRSxLQUFLLENBQUMsRUFBRUcsS0FBS0MsUUFBUSxHQUFHQyxRQUFRLENBQUMsR0FBRyxLQUFLLENBQUM7SUFDdEQ7SUFFQSxVQUFVO0lBQ1YsTUFBTUMsV0FBV3pCLFdBQVcsSUFBSSxjQUFlQSxXQUFZLE1BQU07SUFDakUsTUFBTTBCLGNBQWNuQixhQUFhRSxXQUFXVjtJQUM1QyxNQUFNNEIsa0JBQWtCcEIsYUFBYSxXQUFZUCxXQUFZLE1BQU15QjtJQUVuRSxTQUFTO0lBQ1QsTUFBTUcsb0JBQW9CLENBQUNDO1FBQ3pCLElBQUl2QixZQUFZTixhQUFhLEdBQUc7UUFFaENRLGNBQWM7UUFDZHNCLG9CQUFvQkQ7UUFDcEJBLE1BQU1FLGNBQWM7SUFDdEI7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTUMsa0JBQWtCLENBQUNIO1FBQ3ZCLElBQUksQ0FBQ2QsWUFBWWtCLE9BQU8sSUFBSWpDLGFBQWEsR0FBRztRQUU1QyxNQUFNa0MsT0FBT25CLFlBQVlrQixPQUFPLENBQUNFLHFCQUFxQjtRQUN0RCxNQUFNQyxJQUFJUCxNQUFNUSxPQUFPLEdBQUdILEtBQUtJLElBQUk7UUFDbkMsTUFBTUMsYUFBYW5CLEtBQUtvQixHQUFHLENBQUMsR0FBR3BCLEtBQUtxQixHQUFHLENBQUMsR0FBR0wsSUFBSUYsS0FBS1EsS0FBSztRQUN6RCxNQUFNQyxPQUFPSixhQUFhdkM7UUFFMUJjLGVBQWU2QjtRQUNmL0IsZUFBZTtJQUNqQjtJQUVBLFNBQVM7SUFDVCxNQUFNZ0MsbUJBQW1CO1FBQ3ZCLElBQUksQ0FBQ3JDLFlBQVk7WUFDZkssZUFBZTtRQUNqQjtJQUNGO0lBRUEsVUFBVTtJQUNWLE1BQU1rQixzQkFBc0IsQ0FBQ0Q7UUFDM0IsSUFBSSxDQUFDZCxZQUFZa0IsT0FBTyxJQUFJakMsYUFBYSxHQUFHO1FBRTVDLE1BQU1rQyxPQUFPbkIsWUFBWWtCLE9BQU8sQ0FBQ0UscUJBQXFCO1FBQ3RELE1BQU1DLElBQUlQLE1BQU1RLE9BQU8sR0FBR0gsS0FBS0ksSUFBSTtRQUNuQyxNQUFNQyxhQUFhbkIsS0FBS29CLEdBQUcsQ0FBQyxHQUFHcEIsS0FBS3FCLEdBQUcsQ0FBQyxHQUFHTCxJQUFJRixLQUFLUSxLQUFLO1FBQ3pELE1BQU1DLE9BQU9KLGFBQWF2QztRQUUxQlUsWUFBWWlDO1FBQ1o3QixlQUFlNkI7SUFDakI7SUFFQSxTQUFTO0lBQ1QsTUFBTUUsb0JBQW9CLENBQUNoQjtRQUN6QixJQUFJLENBQUN0QixjQUFjRCxVQUFVO1FBQzdCd0Isb0JBQW9CRDtJQUN0QjtJQUVBLFNBQVM7SUFDVCxNQUFNaUIsa0JBQWtCO1FBQ3RCLElBQUl2QyxZQUFZO1lBQ2ROLE9BQU9RO1lBQ1BELGNBQWM7WUFDZEksZUFBZTtRQUNqQjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1tQyxjQUFjLENBQUNsQjtRQUNuQixJQUFJdkIsWUFBWU4sYUFBYSxLQUFLTyxZQUFZO1FBRTlDLE1BQU0yQixPQUFPbkIsWUFBWWtCLE9BQU8sRUFBRUU7UUFDbEMsSUFBSSxDQUFDRCxNQUFNO1FBRVgsTUFBTUUsSUFBSVAsTUFBTVEsT0FBTyxHQUFHSCxLQUFLSSxJQUFJO1FBQ25DLE1BQU1DLGFBQWFuQixLQUFLb0IsR0FBRyxDQUFDLEdBQUdwQixLQUFLcUIsR0FBRyxDQUFDLEdBQUdMLElBQUlGLEtBQUtRLEtBQUs7UUFDekQsTUFBTUMsT0FBT0osYUFBYXZDO1FBRTFCQyxPQUFPMEM7SUFDVDtJQUVBLE9BQU87SUFDUCxNQUFNSyxnQkFBZ0IsQ0FBQ25CO1FBQ3JCLElBQUl2QixZQUFZTixhQUFhLEdBQUc7UUFFaEMsSUFBSWlELFVBQVVsRDtRQUNkLE1BQU1tRCxPQUFPbEQsV0FBVyxNQUFNLFFBQVE7UUFFdEMsT0FBUTZCLE1BQU1zQixHQUFHO1lBQ2YsS0FBSztnQkFDSEYsVUFBVTdCLEtBQUtvQixHQUFHLENBQUMsR0FBR3pDLGNBQWNtRDtnQkFDcEM7WUFDRixLQUFLO2dCQUNIRCxVQUFVN0IsS0FBS3FCLEdBQUcsQ0FBQ3pDLFVBQVVELGNBQWNtRDtnQkFDM0M7WUFDRixLQUFLO2dCQUNIRCxVQUFVO2dCQUNWO1lBQ0YsS0FBSztnQkFDSEEsVUFBVWpEO2dCQUNWO1lBQ0Y7Z0JBQ0U7UUFDSjtRQUVBNkIsTUFBTUUsY0FBYztRQUNwQjlCLE9BQU9nRDtJQUNUO0lBRUEsV0FBVztJQUNYckQsZ0RBQVNBLENBQUM7UUFDUixJQUFJVyxZQUFZO1lBQ2Q2QyxTQUFTQyxnQkFBZ0IsQ0FBQyxlQUFlUjtZQUN6Q08sU0FBU0MsZ0JBQWdCLENBQUMsYUFBYVA7WUFFdkMsT0FBTztnQkFDTE0sU0FBU0UsbUJBQW1CLENBQUMsZUFBZVQ7Z0JBQzVDTyxTQUFTRSxtQkFBbUIsQ0FBQyxhQUFhUjtZQUM1QztRQUNGO1FBQ0EsT0FBT1M7SUFDVCxHQUFHO1FBQUNoRDtRQUFZRTtLQUFTO0lBRXpCLE1BQU0rQyxjQUFjO1FBQ2xCQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEsTUFBTUMsWUFBWTtRQUNoQkgsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7SUFDTjtJQUVBLHFCQUNFLDhEQUFDRTtRQUFJeEQsV0FBV1IsMENBQUlBLENBQUMsVUFBVVE7O1lBRTVCRiwwQkFDQyw4REFBQzBEO2dCQUFJeEQsV0FBVTs7a0NBQ2IsOERBQUN5RDt3QkFBS3pELFdBQVU7a0NBQ2JXLFdBQVdVOzs7Ozs7a0NBRWQsOERBQUNvQzt3QkFBS3pELFdBQVU7a0NBQ2JXLFdBQVdoQjs7Ozs7Ozs7Ozs7OzBCQU1sQiw4REFBQzZEO2dCQUFJeEQsV0FBVTs7a0NBQ2IsOERBQUN3RDt3QkFDQ0UsS0FBS2hEO3dCQUNMVixXQUFXUiwwQ0FBSUEsQ0FDYiwyRUFDQSx3REFDQTJELFdBQVcsQ0FBQ3BELEtBQUssRUFDakJFLFlBQVk7d0JBRWQwRCxlQUFlcEM7d0JBQ2ZxQyxTQUFTbEI7d0JBQ1RtQixhQUFhbEM7d0JBQ2JtQyxjQUFjdkI7d0JBQ2R3QixXQUFXcEI7d0JBQ1hxQixVQUFVL0QsV0FBVyxDQUFDLElBQUk7d0JBQzFCZ0UsTUFBSzt3QkFDTEMsaUJBQWU7d0JBQ2ZDLGlCQUFleEU7d0JBQ2Z5RSxpQkFBZTFFO3dCQUNmMkUsY0FBVzs7NEJBR1Z4RSwyQkFDQyw4REFBQzJEO2dDQUFJeEQsV0FBVTs7Ozs7OzBDQUlqQiw4REFBQ3dEO2dDQUNDeEQsV0FBV1IsMENBQUlBLENBQ2IsK0VBQ0EyRCxXQUFXLENBQUNwRCxLQUFLO2dDQUVuQnVFLE9BQU87b0NBQUVqQyxPQUFPLENBQUMsRUFBRXRCLEtBQUtvQixHQUFHLENBQUMsR0FBR3BCLEtBQUtxQixHQUFHLENBQUMsS0FBS2Qsa0JBQWtCLENBQUMsQ0FBQztnQ0FBQzs7Ozs7OzRCQU9uRTNCLFdBQVcsbUJBQ1YsOERBQUM2RDtnQ0FDQ3hELFdBQVdSLDBDQUFJQSxDQUNiLDhFQUNBLDJFQUNBLHFDQUNBVSxjQUFjLHlCQUNkcUQsU0FBUyxDQUFDeEQsS0FBSztnQ0FFakJ1RSxPQUFPO29DQUFFckMsTUFBTSxDQUFDLEVBQUVsQixLQUFLb0IsR0FBRyxDQUFDLEdBQUdwQixLQUFLcUIsR0FBRyxDQUFDLEtBQUtkLGtCQUFrQixDQUFDLENBQUM7Z0NBQUM7Ozs7Ozs7Ozs7OztvQkFNdEVoQixlQUFlWCxXQUFXLG1CQUN6Qiw4REFBQzZEO3dCQUNDeEQsV0FBVTt3QkFDVnNFLE9BQU87NEJBQ0xyQyxNQUFNLENBQUMsRUFBRWxCLEtBQUtvQixHQUFHLENBQUMsR0FBR3BCLEtBQUtxQixHQUFHLENBQUMsS0FBSyxjQUFlekMsV0FBWSxNQUFNLENBQUMsQ0FBQzt3QkFDeEU7a0NBRUNnQixXQUFXSDs7Ozs7Ozs7Ozs7O1lBTWpCYixXQUFXLG1CQUNWLDhEQUFDNkQ7Z0JBQUl4RCxXQUFVOztvQkFDWmUsS0FBS3dELEtBQUssQ0FBQ2pEO29CQUFpQjs7Ozs7Ozs7Ozs7OztBQUt2QyIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2NvbXBvbmVudHMvQXVkaW9QbGF5ZXIvUHJvZ3Jlc3NCYXIudHN4P2M1MzQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlUmVmLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjbHN4IH0gZnJvbSAnY2xzeCc7XG5cbmludGVyZmFjZSBQcm9ncmVzc0JhclByb3BzIHtcbiAgY3VycmVudFRpbWU6IG51bWJlcjtcbiAgZHVyYXRpb246IG51bWJlcjtcbiAgb25TZWVrOiAodGltZTogbnVtYmVyKSA9PiB2b2lkO1xuICBpc0xvYWRpbmc/OiBib29sZWFuO1xuICBzaG93VGltZT86IGJvb2xlYW47XG4gIHNpemU/OiAnc20nIHwgJ21kJyB8ICdsZyc7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgZGlzYWJsZWQ/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gUHJvZ3Jlc3NCYXIoe1xuICBjdXJyZW50VGltZSxcbiAgZHVyYXRpb24sXG4gIG9uU2VlayxcbiAgaXNMb2FkaW5nID0gZmFsc2UsXG4gIHNob3dUaW1lID0gdHJ1ZSxcbiAgc2l6ZSA9ICdtZCcsXG4gIGNsYXNzTmFtZSxcbiAgZGlzYWJsZWQgPSBmYWxzZSxcbn06IFByb2dyZXNzQmFyUHJvcHMpIHtcbiAgY29uc3QgW2lzRHJhZ2dpbmcsIHNldElzRHJhZ2dpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZHJhZ1RpbWUsIHNldERyYWdUaW1lXSA9IHVzZVN0YXRlKDApO1xuICBjb25zdCBbc2hvd1Rvb2x0aXAsIHNldFNob3dUb29sdGlwXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Rvb2x0aXBUaW1lLCBzZXRUb29sdGlwVGltZV0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgcHJvZ3Jlc3NSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpO1xuXG4gIC8vIOagvOW8j+WMluaXtumXtOaYvuekulxuICBjb25zdCBmb3JtYXRUaW1lID0gKHNlY29uZHM6IG51bWJlcik6IHN0cmluZyA9PiB7XG4gICAgaWYgKCFpc0Zpbml0ZShzZWNvbmRzKSB8fCBzZWNvbmRzIDwgMCkgcmV0dXJuICcwOjAwJztcbiAgICBcbiAgICBjb25zdCBtaW5zID0gTWF0aC5mbG9vcihzZWNvbmRzIC8gNjApO1xuICAgIGNvbnN0IHNlY3MgPSBNYXRoLmZsb29yKHNlY29uZHMgJSA2MCk7XG4gICAgcmV0dXJuIGAke21pbnN9OiR7c2Vjcy50b1N0cmluZygpLnBhZFN0YXJ0KDIsICcwJyl9YDtcbiAgfTtcblxuICAvLyDorqHnrpfov5vluqbnmb7liIbmr5RcbiAgY29uc3QgcHJvZ3Jlc3MgPSBkdXJhdGlvbiA+IDAgPyAoY3VycmVudFRpbWUgLyBkdXJhdGlvbikgKiAxMDAgOiAwO1xuICBjb25zdCBkaXNwbGF5VGltZSA9IGlzRHJhZ2dpbmcgPyBkcmFnVGltZSA6IGN1cnJlbnRUaW1lO1xuICBjb25zdCBkaXNwbGF5UHJvZ3Jlc3MgPSBpc0RyYWdnaW5nID8gKGRyYWdUaW1lIC8gZHVyYXRpb24pICogMTAwIDogcHJvZ3Jlc3M7XG5cbiAgLy8g5aSE55CG5ouW5ou95byA5aeLXG4gIGNvbnN0IGhhbmRsZVBvaW50ZXJEb3duID0gKGV2ZW50OiBSZWFjdC5Qb2ludGVyRXZlbnQpID0+IHtcbiAgICBpZiAoZGlzYWJsZWQgfHwgZHVyYXRpb24gPT09IDApIHJldHVybjtcbiAgICBcbiAgICBzZXRJc0RyYWdnaW5nKHRydWUpO1xuICAgIHVwZGF0ZVRpbWVGcm9tRXZlbnQoZXZlbnQpO1xuICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gIH07XG5cbiAgLy8g5aSE55CG6byg5qCH56e75Yqo77yI55So5LqO5pi+56S66aKE6KeI5pe26Ze077yJXG4gIGNvbnN0IGhhbmRsZU1vdXNlTW92ZSA9IChldmVudDogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGlmICghcHJvZ3Jlc3NSZWYuY3VycmVudCB8fCBkdXJhdGlvbiA9PT0gMCkgcmV0dXJuO1xuICAgIFxuICAgIGNvbnN0IHJlY3QgPSBwcm9ncmVzc1JlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgIGNvbnN0IHggPSBldmVudC5jbGllbnRYIC0gcmVjdC5sZWZ0O1xuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCB4IC8gcmVjdC53aWR0aCkpO1xuICAgIGNvbnN0IHRpbWUgPSBwZXJjZW50YWdlICogZHVyYXRpb247XG4gICAgXG4gICAgc2V0VG9vbHRpcFRpbWUodGltZSk7XG4gICAgc2V0U2hvd1Rvb2x0aXAodHJ1ZSk7XG4gIH07XG5cbiAgLy8g5aSE55CG6byg5qCH56a75byAXG4gIGNvbnN0IGhhbmRsZU1vdXNlTGVhdmUgPSAoKSA9PiB7XG4gICAgaWYgKCFpc0RyYWdnaW5nKSB7XG4gICAgICBzZXRTaG93VG9vbHRpcChmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOS7juS6i+S7tuabtOaWsOaXtumXtFxuICBjb25zdCB1cGRhdGVUaW1lRnJvbUV2ZW50ID0gKGV2ZW50OiBQb2ludGVyRXZlbnQgfCBSZWFjdC5Qb2ludGVyRXZlbnQpID0+IHtcbiAgICBpZiAoIXByb2dyZXNzUmVmLmN1cnJlbnQgfHwgZHVyYXRpb24gPT09IDApIHJldHVybjtcblxuICAgIGNvbnN0IHJlY3QgPSBwcm9ncmVzc1JlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgIGNvbnN0IHggPSBldmVudC5jbGllbnRYIC0gcmVjdC5sZWZ0O1xuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCB4IC8gcmVjdC53aWR0aCkpO1xuICAgIGNvbnN0IHRpbWUgPSBwZXJjZW50YWdlICogZHVyYXRpb247XG4gICAgXG4gICAgc2V0RHJhZ1RpbWUodGltZSk7XG4gICAgc2V0VG9vbHRpcFRpbWUodGltZSk7XG4gIH07XG5cbiAgLy8g5aSE55CG5ouW5ou956e75YqoXG4gIGNvbnN0IGhhbmRsZVBvaW50ZXJNb3ZlID0gKGV2ZW50OiBQb2ludGVyRXZlbnQpID0+IHtcbiAgICBpZiAoIWlzRHJhZ2dpbmcgfHwgZGlzYWJsZWQpIHJldHVybjtcbiAgICB1cGRhdGVUaW1lRnJvbUV2ZW50KGV2ZW50KTtcbiAgfTtcblxuICAvLyDlpITnkIbmi5bmi73nu5PmnZ9cbiAgY29uc3QgaGFuZGxlUG9pbnRlclVwID0gKCkgPT4ge1xuICAgIGlmIChpc0RyYWdnaW5nKSB7XG4gICAgICBvblNlZWsoZHJhZ1RpbWUpO1xuICAgICAgc2V0SXNEcmFnZ2luZyhmYWxzZSk7XG4gICAgICBzZXRTaG93VG9vbHRpcChmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOWkhOeQhueCueWHu+i3s+i9rFxuICBjb25zdCBoYW5kbGVDbGljayA9IChldmVudDogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGlmIChkaXNhYmxlZCB8fCBkdXJhdGlvbiA9PT0gMCB8fCBpc0RyYWdnaW5nKSByZXR1cm47XG4gICAgXG4gICAgY29uc3QgcmVjdCA9IHByb2dyZXNzUmVmLmN1cnJlbnQ/LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpO1xuICAgIGlmICghcmVjdCkgcmV0dXJuO1xuICAgIFxuICAgIGNvbnN0IHggPSBldmVudC5jbGllbnRYIC0gcmVjdC5sZWZ0O1xuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBNYXRoLm1heCgwLCBNYXRoLm1pbigxLCB4IC8gcmVjdC53aWR0aCkpO1xuICAgIGNvbnN0IHRpbWUgPSBwZXJjZW50YWdlICogZHVyYXRpb247XG4gICAgXG4gICAgb25TZWVrKHRpbWUpO1xuICB9O1xuXG4gIC8vIOmUruebmOaOp+WItlxuICBjb25zdCBoYW5kbGVLZXlEb3duID0gKGV2ZW50OiBSZWFjdC5LZXlib2FyZEV2ZW50KSA9PiB7XG4gICAgaWYgKGRpc2FibGVkIHx8IGR1cmF0aW9uID09PSAwKSByZXR1cm47XG5cbiAgICBsZXQgbmV3VGltZSA9IGN1cnJlbnRUaW1lO1xuICAgIGNvbnN0IHN0ZXAgPSBkdXJhdGlvbiAqIDAuMDU7IC8vIDUlIOatpemVv1xuXG4gICAgc3dpdGNoIChldmVudC5rZXkpIHtcbiAgICAgIGNhc2UgJ0Fycm93TGVmdCc6XG4gICAgICAgIG5ld1RpbWUgPSBNYXRoLm1heCgwLCBjdXJyZW50VGltZSAtIHN0ZXApO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ0Fycm93UmlnaHQnOlxuICAgICAgICBuZXdUaW1lID0gTWF0aC5taW4oZHVyYXRpb24sIGN1cnJlbnRUaW1lICsgc3RlcCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnSG9tZSc6XG4gICAgICAgIG5ld1RpbWUgPSAwO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ0VuZCc6XG4gICAgICAgIG5ld1RpbWUgPSBkdXJhdGlvbjtcbiAgICAgICAgYnJlYWs7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICBvblNlZWsobmV3VGltZSk7XG4gIH07XG5cbiAgLy8g55uR5ZCs5YWo5bGA6byg5qCH5LqL5Lu2XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzRHJhZ2dpbmcpIHtcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJtb3ZlJywgaGFuZGxlUG9pbnRlck1vdmUpO1xuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigncG9pbnRlcnVwJywgaGFuZGxlUG9pbnRlclVwKTtcblxuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigncG9pbnRlcm1vdmUnLCBoYW5kbGVQb2ludGVyTW92ZSk7XG4gICAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3BvaW50ZXJ1cCcsIGhhbmRsZVBvaW50ZXJVcCk7XG4gICAgICB9O1xuICAgIH1cbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9LCBbaXNEcmFnZ2luZywgZHJhZ1RpbWVdKTtcblxuICBjb25zdCBzaXplQ2xhc3NlcyA9IHtcbiAgICBzbTogJ2gtMScsXG4gICAgbWQ6ICdoLTInLFxuICAgIGxnOiAnaC0zJyxcbiAgfTtcblxuICBjb25zdCB0aHVtYlNpemUgPSB7XG4gICAgc206ICd3LTMgaC0zJyxcbiAgICBtZDogJ3ctNCBoLTQnLFxuICAgIGxnOiAndy01IGgtNScsXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzeCgndy1mdWxsJywgY2xhc3NOYW1lKX0+XG4gICAgICB7Lyog5pe26Ze05pi+56S6ICovfVxuICAgICAge3Nob3dUaW1lICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgbWItMiB0ZXh0LXNtIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tb25vXCI+XG4gICAgICAgICAgICB7Zm9ybWF0VGltZShkaXNwbGF5VGltZSl9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbW9ub1wiPlxuICAgICAgICAgICAge2Zvcm1hdFRpbWUoZHVyYXRpb24pfVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog6L+b5bqm5p2h5a655ZmoICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICA8ZGl2XG4gICAgICAgICAgcmVmPXtwcm9ncmVzc1JlZn1cbiAgICAgICAgICBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgICAgICAncmVsYXRpdmUgYmctZ3JheS0yMDAgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkLWZ1bGwgY3Vyc29yLXBvaW50ZXIgZ3JvdXAnLFxuICAgICAgICAgICAgJ2ZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1hbWJlci01MDAnLFxuICAgICAgICAgICAgc2l6ZUNsYXNzZXNbc2l6ZV0sXG4gICAgICAgICAgICBkaXNhYmxlZCAmJiAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnXG4gICAgICAgICAgKX1cbiAgICAgICAgICBvblBvaW50ZXJEb3duPXtoYW5kbGVQb2ludGVyRG93bn1cbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDbGlja31cbiAgICAgICAgICBvbk1vdXNlTW92ZT17aGFuZGxlTW91c2VNb3ZlfVxuICAgICAgICAgIG9uTW91c2VMZWF2ZT17aGFuZGxlTW91c2VMZWF2ZX1cbiAgICAgICAgICBvbktleURvd249e2hhbmRsZUtleURvd259XG4gICAgICAgICAgdGFiSW5kZXg9e2Rpc2FibGVkID8gLTEgOiAwfVxuICAgICAgICAgIHJvbGU9XCJzbGlkZXJcIlxuICAgICAgICAgIGFyaWEtdmFsdWVtaW49ezB9XG4gICAgICAgICAgYXJpYS12YWx1ZW1heD17ZHVyYXRpb259XG4gICAgICAgICAgYXJpYS12YWx1ZW5vdz17Y3VycmVudFRpbWV9XG4gICAgICAgICAgYXJpYS1sYWJlbD1cIumfs+mikei/m+W6plwiXG4gICAgICAgID5cbiAgICAgICAgICB7Lyog5Yqg6L2954q25oCBICovfVxuICAgICAgICAgIHtpc0xvYWRpbmcgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYXktMzAwIGRhcms6YmctZ3JheS02MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7Lyog5bey5pKt5pS+6L+b5bqmICovfVxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT17Y2xzeChcbiAgICAgICAgICAgICAgJ2Fic29sdXRlIGxlZnQtMCB0b3AtMCBiZy1hbWJlci01MDAgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTE1MCcsXG4gICAgICAgICAgICAgIHNpemVDbGFzc2VzW3NpemVdXG4gICAgICAgICAgICApfVxuICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke01hdGgubWF4KDAsIE1hdGgubWluKDEwMCwgZGlzcGxheVByb2dyZXNzKSl9JWAgfX1cbiAgICAgICAgICAvPlxuXG4gICAgICAgICAgey8qIOe8k+WGsui/m+W6pu+8iOWmguaenOmcgOimgeeahOivne+8iSAqL31cbiAgICAgICAgICB7Lyog6L+Z6YeM5Y+v5Lul5re75Yqg57yT5Yay6L+b5bqm5pi+56S6ICovfVxuXG4gICAgICAgICAgey8qIOaLluaLveaJi+afhCAqL31cbiAgICAgICAgICB7ZHVyYXRpb24gPiAwICYmIChcbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHN4KFxuICAgICAgICAgICAgICAgICdhYnNvbHV0ZSB0b3AtMS8yIGJnLXdoaXRlIGJvcmRlci0yIGJvcmRlci1hbWJlci01MDAgcm91bmRlZC1mdWxsIHNoYWRvdy1tZCcsXG4gICAgICAgICAgICAgICAgJ3RyYW5zZm9ybSAtdHJhbnNsYXRlLXgtMS8yIC10cmFuc2xhdGUteS0xLzIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMTUwJyxcbiAgICAgICAgICAgICAgICAnb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwJyxcbiAgICAgICAgICAgICAgICBpc0RyYWdnaW5nICYmICdvcGFjaXR5LTEwMCBzY2FsZS0xMjUnLFxuICAgICAgICAgICAgICAgIHRodW1iU2l6ZVtzaXplXVxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICBzdHlsZT17eyBsZWZ0OiBgJHtNYXRoLm1heCgwLCBNYXRoLm1pbigxMDAsIGRpc3BsYXlQcm9ncmVzcykpfSVgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDml7bpl7Tmj5DnpLogKi99XG4gICAgICAgIHtzaG93VG9vbHRpcCAmJiBkdXJhdGlvbiA+IDAgJiYgKFxuICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHotMTAgcHgtMiBweS0xIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBiZy1ncmF5LTkwMCByb3VuZGVkIHNoYWRvdy1sZyB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiAtdG9wLThcIlxuICAgICAgICAgICAgc3R5bGU9e3sgXG4gICAgICAgICAgICAgIGxlZnQ6IGAke01hdGgubWF4KDAsIE1hdGgubWluKDEwMCwgKHRvb2x0aXBUaW1lIC8gZHVyYXRpb24pICogMTAwKSl9JWAgXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtmb3JtYXRUaW1lKHRvb2x0aXBUaW1lKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog6L+b5bqm55m+5YiG5q+U77yI5Y+v6YCJ77yJICovfVxuICAgICAge2R1cmF0aW9uID4gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXhzIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktNTAwIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAge01hdGgucm91bmQoZGlzcGxheVByb2dyZXNzKX0lXG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsImNsc3giLCJQcm9ncmVzc0JhciIsImN1cnJlbnRUaW1lIiwiZHVyYXRpb24iLCJvblNlZWsiLCJpc0xvYWRpbmciLCJzaG93VGltZSIsInNpemUiLCJjbGFzc05hbWUiLCJkaXNhYmxlZCIsImlzRHJhZ2dpbmciLCJzZXRJc0RyYWdnaW5nIiwiZHJhZ1RpbWUiLCJzZXREcmFnVGltZSIsInNob3dUb29sdGlwIiwic2V0U2hvd1Rvb2x0aXAiLCJ0b29sdGlwVGltZSIsInNldFRvb2x0aXBUaW1lIiwicHJvZ3Jlc3NSZWYiLCJmb3JtYXRUaW1lIiwic2Vjb25kcyIsImlzRmluaXRlIiwibWlucyIsIk1hdGgiLCJmbG9vciIsInNlY3MiLCJ0b1N0cmluZyIsInBhZFN0YXJ0IiwicHJvZ3Jlc3MiLCJkaXNwbGF5VGltZSIsImRpc3BsYXlQcm9ncmVzcyIsImhhbmRsZVBvaW50ZXJEb3duIiwiZXZlbnQiLCJ1cGRhdGVUaW1lRnJvbUV2ZW50IiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVNb3VzZU1vdmUiLCJjdXJyZW50IiwicmVjdCIsImdldEJvdW5kaW5nQ2xpZW50UmVjdCIsIngiLCJjbGllbnRYIiwibGVmdCIsInBlcmNlbnRhZ2UiLCJtYXgiLCJtaW4iLCJ3aWR0aCIsInRpbWUiLCJoYW5kbGVNb3VzZUxlYXZlIiwiaGFuZGxlUG9pbnRlck1vdmUiLCJoYW5kbGVQb2ludGVyVXAiLCJoYW5kbGVDbGljayIsImhhbmRsZUtleURvd24iLCJuZXdUaW1lIiwic3RlcCIsImtleSIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJ1bmRlZmluZWQiLCJzaXplQ2xhc3NlcyIsInNtIiwibWQiLCJsZyIsInRodW1iU2l6ZSIsImRpdiIsInNwYW4iLCJyZWYiLCJvblBvaW50ZXJEb3duIiwib25DbGljayIsIm9uTW91c2VNb3ZlIiwib25Nb3VzZUxlYXZlIiwib25LZXlEb3duIiwidGFiSW5kZXgiLCJyb2xlIiwiYXJpYS12YWx1ZW1pbiIsImFyaWEtdmFsdWVtYXgiLCJhcmlhLXZhbHVlbm93IiwiYXJpYS1sYWJlbCIsInN0eWxlIiwicm91bmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/AudioPlayer/StandardPlayer.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StandardPlayer: () => (/* binding */ StandardPlayer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(ssr)/./src/hooks/useAudioPlayer.ts\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PlayButton */ \"(ssr)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VolumeControl */ \"(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProgressBar */ \"(ssr)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/StopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ StandardPlayer auto */ \n\n\n\n\n\n\n\n\n\n\nfunction StandardPlayer({ className, position = \"bottom\", showMixingButton = true, showSleepModeButton = true, autoHide = false, autoHideDelay = 5000 }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"audioPlayer\");\n    const tCommon = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"common\");\n    const { currentSound, playerUI, favorites, setPlayerVisible, setPlayerMode, togglePlayerMinimized, setTimerPanelVisible, setMixingPanelVisible, addToFavorites, removeFromFavorites } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore)();\n    const { play, pause, stop, setVolume, seek, isPlaying, isPaused, isLoading, currentTime, duration, volume, error } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__.useAudioPlayer)();\n    // 当有音频播放时自动显示播放器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentSound && !playerUI.isVisible) {\n            setPlayerVisible(true);\n        }\n    }, [\n        currentSound,\n        playerUI.isVisible,\n        setPlayerVisible\n    ]);\n    // 自动隐藏功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoHide && !isPlaying && playerUI.isVisible) {\n            const timer = setTimeout(()=>{\n                setPlayerVisible(false);\n            }, autoHideDelay);\n            return ()=>clearTimeout(timer);\n        }\n        return undefined;\n    }, [\n        autoHide,\n        isPlaying,\n        playerUI.isVisible,\n        autoHideDelay,\n        setPlayerVisible\n    ]);\n    // 播放控制处理\n    const handlePlay = ()=>{\n        if (currentSound) {\n            play();\n        }\n    };\n    const handlePause = ()=>{\n        pause();\n    };\n    const handleStop = ()=>{\n        stop();\n        setPlayerVisible(false);\n    };\n    const handleVolumeChange = (newVolume)=>{\n        setVolume(newVolume);\n    };\n    const handleSeek = (position)=>{\n        seek(position);\n    };\n    // 收藏控制\n    const isFavorite = currentSound ? favorites.includes(currentSound.id) : false;\n    const handleToggleFavorite = ()=>{\n        if (currentSound) {\n            if (isFavorite) {\n                removeFromFavorites(currentSound.id);\n            } else {\n                addToFavorites(currentSound.id);\n            }\n        }\n    };\n    // 面板控制\n    const handleTimerClick = ()=>{\n        setTimerPanelVisible(!playerUI.showTimerPanel);\n    };\n    const handleMixingClick = ()=>{\n        setMixingPanelVisible(!playerUI.showMixingPanel);\n    };\n    const handleSleepModeClick = ()=>{\n        setPlayerMode(\"sleep\");\n    };\n    const handleMinimize = ()=>{\n        togglePlayerMinimized();\n    };\n    const handleClose = ()=>{\n        stop();\n        setPlayerVisible(false);\n    };\n    // 位置样式 - 响应式优化\n    const positionClasses = {\n        bottom: \"fixed bottom-0 left-0 right-0 z-50\",\n        top: \"fixed top-0 left-0 right-0 z-50\",\n        floating: \"fixed bottom-4 left-2 right-2 sm:left-4 sm:right-4 z-50 max-w-4xl mx-auto\"\n    };\n    // 动画变体\n    const playerVariants = {\n        hidden: {\n            y: position === \"bottom\" ? 100 : position === \"top\" ? -100 : 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 300,\n                damping: 30\n            }\n        },\n        exit: {\n            y: position === \"bottom\" ? 100 : position === \"top\" ? -100 : 20,\n            opacity: 0,\n            transition: {\n                duration: 0.2\n            }\n        }\n    };\n    const minimizedVariants = {\n        normal: {\n            height: \"auto\"\n        },\n        minimized: {\n            height: 60,\n            transition: {\n                duration: 0.3\n            }\n        }\n    };\n    if (!playerUI.isVisible || !currentSound) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n            initial: \"hidden\",\n            animate: \"visible\",\n            exit: \"exit\",\n            variants: playerVariants,\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(positionClasses[position], \"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md\", \"border-t border-gray-200 dark:border-gray-700\", position === \"floating\" && \"rounded-lg border shadow-xl\", className),\n            \"data-testid\": \"standard-player\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                animate: playerUI.isMinimized ? \"minimized\" : \"normal\",\n                variants: minimizedVariants,\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 sm:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1 sm:gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_5__.PlayButton, {\n                                                        isPlaying: isPlaying,\n                                                        isLoading: isLoading,\n                                                        onPlay: handlePlay,\n                                                        onPause: handlePause,\n                                                        size: \"md\",\n                                                        variant: \"primary\",\n                                                        disabled: !currentSound\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleStop,\n                                                        disabled: !currentSound,\n                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"text-gray-600 dark:text-gray-400\"),\n                                                        \"aria-label\": t(\"controls.stop\"),\n                                                        title: t(\"controls.stop\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                        children: currentSound.title.zh || currentSound.title.en\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                        children: currentSound.description?.zh || currentSound.description?.en\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this),\n                                    !playerUI.isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_7__.ProgressBar, {\n                                            currentTime: currentTime,\n                                            duration: duration,\n                                            onSeek: handleSeek,\n                                            isLoading: isLoading,\n                                            showTime: true,\n                                            size: \"md\",\n                                            disabled: !currentSound\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 sm:gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_6__.VolumeControl, {\n                                            volume: volume,\n                                            onVolumeChange: handleVolumeChange,\n                                            size: \"sm\",\n                                            showIcon: true,\n                                            disabled: !currentSound\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this),\n                                    !playerUI.isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTimerClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"hidden sm:block p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", playerUI.showTimerPanel ? \"text-amber-600 bg-amber-50 dark:bg-amber-900/20\" : \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"timer.setTimer\"),\n                                                title: t(\"timer.setTimer\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 21\n                                            }, this),\n                                            showMixingButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleMixingClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"hidden sm:block p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", playerUI.showMixingPanel ? \"text-amber-600 bg-amber-50 dark:bg-amber-900/20\" : \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"mixing.title\"),\n                                                title: t(\"mixing.title\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 23\n                                            }, this),\n                                            showSleepModeButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSleepModeClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"modes.switchToSleep\"),\n                                                title: t(\"modes.switchToSleep\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 ml-2 border-l border-gray-200 dark:border-gray-700 pl-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleMinimize,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1.5 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-500 dark:text-gray-400\"),\n                                                \"aria-label\": playerUI.isMinimized ? t(\"controls.maximize\") : t(\"controls.minimize\"),\n                                                title: playerUI.isMinimized ? t(\"controls.maximize\") : t(\"controls.minimize\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClose,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1.5 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-500 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"controls.close\"),\n                                                title: t(\"controls.close\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/StandardPlayer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AudioPlayer/VolumeControl.tsx":
/*!******************************************************!*\
  !*** ./src/components/AudioPlayer/VolumeControl.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VolumeControl: () => (/* binding */ VolumeControl)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(ssr)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ VolumeControl auto */ \n\n\n\nfunction VolumeControl({ volume, onVolumeChange, orientation = \"horizontal\", size = \"md\", showIcon = true, showValue = false, className, disabled = false }) {\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"common\");\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTooltip, setShowTooltip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sliderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [previousVolume, setPreviousVolume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(volume);\n    // 处理音量变化\n    const handleVolumeChange = (newVolume)=>{\n        const clampedVolume = Math.max(0, Math.min(1, newVolume));\n        onVolumeChange(clampedVolume);\n    };\n    // 处理鼠标/触摸事件\n    const handlePointerDown = (event)=>{\n        if (disabled) return;\n        setIsDragging(true);\n        setShowTooltip(true);\n        updateVolumeFromEvent(event);\n        // 阻止默认行为\n        event.preventDefault();\n    };\n    const handlePointerMove = (event)=>{\n        if (!isDragging || disabled) return;\n        updateVolumeFromEvent(event);\n    };\n    const handlePointerUp = ()=>{\n        setIsDragging(false);\n        setShowTooltip(false);\n    };\n    // 从事件更新音量\n    const updateVolumeFromEvent = (event)=>{\n        if (!sliderRef.current) return;\n        const rect = sliderRef.current.getBoundingClientRect();\n        let newVolume;\n        if (orientation === \"horizontal\") {\n            const x = event.clientX - rect.left;\n            newVolume = x / rect.width;\n        } else {\n            const y = event.clientY - rect.top;\n            newVolume = 1 - y / rect.height; // 垂直方向反转\n        }\n        handleVolumeChange(newVolume);\n    };\n    // 键盘控制\n    const handleKeyDown = (event)=>{\n        if (disabled) return;\n        let newVolume = volume;\n        const step = 0.1;\n        switch(event.key){\n            case \"ArrowUp\":\n            case \"ArrowRight\":\n                newVolume = Math.min(1, volume + step);\n                break;\n            case \"ArrowDown\":\n            case \"ArrowLeft\":\n                newVolume = Math.max(0, volume - step);\n                break;\n            case \"Home\":\n                newVolume = 1;\n                break;\n            case \"End\":\n                newVolume = 0;\n                break;\n            default:\n                return;\n        }\n        event.preventDefault();\n        handleVolumeChange(newVolume);\n    };\n    // 静音切换\n    const toggleMute = ()=>{\n        if (disabled) return;\n        if (volume > 0) {\n            setPreviousVolume(volume);\n            handleVolumeChange(0);\n        } else {\n            handleVolumeChange(previousVolume > 0 ? previousVolume : 0.7);\n        }\n    };\n    // 监听全局鼠标事件\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDragging) {\n            document.addEventListener(\"pointermove\", handlePointerMove);\n            document.addEventListener(\"pointerup\", handlePointerUp);\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp);\n            };\n        }\n        return undefined;\n    }, [\n        isDragging\n    ]);\n    // 获取音量图标\n    const getVolumeIcon = ()=>{\n        if (volume === 0) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this);\n        } else if (volume < 0.3) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M7 9v6h4l5 5V4l-5 5H7z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this);\n        } else if (volume < 0.7) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this);\n        } else {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this);\n        }\n    };\n    const sizeClasses = {\n        sm: orientation === \"horizontal\" ? \"h-1\" : \"w-1 h-16\",\n        md: orientation === \"horizontal\" ? \"h-2\" : \"w-2 h-20\",\n        lg: orientation === \"horizontal\" ? \"h-3\" : \"w-3 h-24\"\n    };\n    const thumbSize = {\n        sm: \"w-3 h-3\",\n        md: \"w-4 h-4\",\n        lg: \"w-5 h-5\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center gap-2\", orientation === \"vertical\" && \"flex-col\", className),\n        children: [\n            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: toggleMute,\n                disabled: disabled,\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"disabled:opacity-50 disabled:cursor-not-allowed\", volume === 0 ? \"text-red-500\" : \"text-gray-600 dark:text-gray-400\"),\n                \"aria-label\": volume === 0 ? \"取消静音\" : \"静音\",\n                title: volume === 0 ? \"取消静音\" : \"静音\",\n                children: getVolumeIcon()\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative flex-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: sliderRef,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"relative bg-gray-200 dark:bg-gray-700 rounded-full cursor-pointer\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", sizeClasses[size], disabled && \"opacity-50 cursor-not-allowed\"),\n                        onPointerDown: handlePointerDown,\n                        onKeyDown: handleKeyDown,\n                        tabIndex: disabled ? -1 : 0,\n                        role: \"slider\",\n                        \"aria-valuemin\": 0,\n                        \"aria-valuemax\": 100,\n                        \"aria-valuenow\": Math.round(volume * 100),\n                        \"aria-label\": t(\"volume\"),\n                        onMouseEnter: ()=>setShowTooltip(true),\n                        onMouseLeave: ()=>!isDragging && setShowTooltip(false),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bg-amber-500 rounded-full transition-all duration-150\",\n                                style: {\n                                    [orientation === \"horizontal\" ? \"width\" : \"height\"]: `${volume * 100}%`,\n                                    [orientation === \"horizontal\" ? \"height\" : \"width\"]: \"100%\",\n                                    [orientation === \"vertical\" ? \"bottom\" : \"left\"]: 0\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute bg-white border-2 border-amber-500 rounded-full shadow-md\", \"transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150\", \"hover:scale-110\", isDragging && \"scale-125\", thumbSize[size]),\n                                style: {\n                                    [orientation === \"horizontal\" ? \"left\" : \"bottom\"]: `${volume * 100}%`,\n                                    [orientation === \"horizontal\" ? \"top\" : \"left\"]: \"50%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    (showTooltip || showValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg\", \"transform -translate-x-1/2\", orientation === \"horizontal\" ? \"-top-8 left-1/2\" : \"-right-12 top-1/2 -translate-y-1/2\"),\n                        children: [\n                            Math.round(volume * 100),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            showValue && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm font-medium text-gray-600 dark:text-gray-400 min-w-[3rem] text-right\",\n                children: [\n                    Math.round(volume * 100),\n                    \"%\"\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AudioPlayer/VolumeControl.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/audioData.ts":
/*!*******************************!*\
  !*** ./src/data/audioData.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   audioData: () => (/* binding */ audioData)\n/* harmony export */ });\nconst audioData = [\n    // Rain Category - 匹配实际文件 (/Sounds/Rain/)\n    {\n        id: \"rain_heavy_rain\",\n        filename: \"heavy-rain.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Heavy Rain\",\n            zh: \"大雨\"\n        },\n        description: {\n            en: \"Intense rainfall for deep relaxation\",\n            zh: \"强烈降雨，深度放松\"\n        },\n        tags: [\n            \"intense\",\n            \"powerful\",\n            \"relaxing\"\n        ],\n        duration: 3600,\n        scientificRating: 8.9,\n        sleepEffectiveness: 9.0,\n        focusEffectiveness: 7.5\n    },\n    {\n        id: \"rain_light_rain\",\n        filename: \"light-rain.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Light Rain\",\n            zh: \"轻雨\"\n        },\n        description: {\n            en: \"Gentle light rain for peaceful relaxation\",\n            zh: \"轻柔细雨，宁静放松\"\n        },\n        tags: [\n            \"gentle\",\n            \"soft\",\n            \"peaceful\"\n        ],\n        duration: 3600,\n        scientificRating: 9.2,\n        sleepEffectiveness: 9.5,\n        focusEffectiveness: 8.8\n    },\n    {\n        id: \"rain_on_car_roof\",\n        filename: \"rain-on-car-roof.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Rain on Car Roof\",\n            zh: \"雨打车顶\"\n        },\n        description: {\n            en: \"Rain drumming on car roof for cozy atmosphere\",\n            zh: \"雨滴敲打车顶，营造温馨氛围\"\n        },\n        tags: [\n            \"cozy\",\n            \"rhythmic\",\n            \"comforting\"\n        ],\n        duration: 3600,\n        scientificRating: 8.5,\n        sleepEffectiveness: 8.7,\n        focusEffectiveness: 8.0\n    },\n    {\n        id: \"rain_on_leaves\",\n        filename: \"rain-on-leaves.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Rain on Leaves\",\n            zh: \"雨打树叶\"\n        },\n        description: {\n            en: \"Rain falling on leaves for natural ambiance\",\n            zh: \"雨滴落在树叶上，自然环境音\"\n        },\n        tags: [\n            \"natural\",\n            \"organic\",\n            \"soothing\"\n        ],\n        duration: 3600,\n        scientificRating: 8.8,\n        sleepEffectiveness: 9.1,\n        focusEffectiveness: 8.3\n    },\n    {\n        id: \"rain_on_tent\",\n        filename: \"rain-on-tent.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Rain on Tent\",\n            zh: \"雨打帐篷\"\n        },\n        description: {\n            en: \"Rain pattering on tent fabric for camping atmosphere\",\n            zh: \"雨滴敲打帐篷，露营氛围\"\n        },\n        tags: [\n            \"camping\",\n            \"adventure\",\n            \"cozy\"\n        ],\n        duration: 3600,\n        scientificRating: 8.4,\n        sleepEffectiveness: 8.6,\n        focusEffectiveness: 7.8\n    },\n    {\n        id: \"rain_on_umbrella\",\n        filename: \"rain-on-umbrella.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Rain on Umbrella\",\n            zh: \"雨打雨伞\"\n        },\n        description: {\n            en: \"Rain hitting umbrella for intimate sound experience\",\n            zh: \"雨滴敲打雨伞，亲密的声音体验\"\n        },\n        tags: [\n            \"intimate\",\n            \"close\",\n            \"personal\"\n        ],\n        duration: 3600,\n        scientificRating: 8.3,\n        sleepEffectiveness: 8.5,\n        focusEffectiveness: 7.9\n    },\n    {\n        id: \"rain_on_window\",\n        filename: \"rain-on-window.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Rain on Window\",\n            zh: \"雨打窗户\"\n        },\n        description: {\n            en: \"Rain streaming down window glass for indoor comfort\",\n            zh: \"雨水流淌在窗玻璃上，室内舒适感\"\n        },\n        tags: [\n            \"indoor\",\n            \"comfort\",\n            \"peaceful\"\n        ],\n        duration: 3600,\n        scientificRating: 8.7,\n        sleepEffectiveness: 8.9,\n        focusEffectiveness: 8.1\n    },\n    {\n        id: \"rain_thunder\",\n        filename: \"thunder.mp3\",\n        category: \"rain\",\n        title: {\n            en: \"Thunder\",\n            zh: \"雷声\"\n        },\n        description: {\n            en: \"Thunder sounds for dramatic weather atmosphere\",\n            zh: \"雷声，戏剧性天气氛围\"\n        },\n        tags: [\n            \"dramatic\",\n            \"powerful\",\n            \"weather\"\n        ],\n        duration: 3600,\n        scientificRating: 7.8,\n        sleepEffectiveness: 7.5,\n        focusEffectiveness: 6.8\n    },\n    // Nature Category - 匹配实际文件 (/Sounds/Nature/)\n    {\n        id: \"nature_campfire\",\n        filename: \"campfire.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Campfire\",\n            zh: \"篝火\"\n        },\n        description: {\n            en: \"Crackling campfire for cozy outdoor atmosphere\",\n            zh: \"噼啪作响的篝火，温馨户外氛围\"\n        },\n        tags: [\n            \"cozy\",\n            \"warm\",\n            \"outdoor\"\n        ],\n        duration: 3600,\n        scientificRating: 8.6,\n        sleepEffectiveness: 8.8,\n        focusEffectiveness: 8.2\n    },\n    {\n        id: \"nature_droplets\",\n        filename: \"droplets.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Water Droplets\",\n            zh: \"水滴\"\n        },\n        description: {\n            en: \"Gentle water droplets for meditation\",\n            zh: \"轻柔水滴声，适合冥想\"\n        },\n        tags: [\n            \"gentle\",\n            \"meditation\",\n            \"pure\"\n        ],\n        duration: 3600,\n        scientificRating: 9.0,\n        sleepEffectiveness: 9.3,\n        focusEffectiveness: 8.7\n    },\n    {\n        id: \"nature_howling_wind\",\n        filename: \"howling-wind.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Howling Wind\",\n            zh: \"呼啸风声\"\n        },\n        description: {\n            en: \"Wind howling through landscapes\",\n            zh: \"风声呼啸穿过大地\"\n        },\n        tags: [\n            \"wind\",\n            \"atmospheric\",\n            \"dramatic\"\n        ],\n        duration: 3600,\n        scientificRating: 7.9,\n        sleepEffectiveness: 7.8,\n        focusEffectiveness: 7.2\n    },\n    {\n        id: \"nature_jungle\",\n        filename: \"jungle.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Jungle\",\n            zh: \"丛林\"\n        },\n        description: {\n            en: \"Tropical jungle ambiance with wildlife\",\n            zh: \"热带丛林环境音，伴有野生动物声\"\n        },\n        tags: [\n            \"tropical\",\n            \"wildlife\",\n            \"exotic\"\n        ],\n        duration: 3600,\n        scientificRating: 8.4,\n        sleepEffectiveness: 8.2,\n        focusEffectiveness: 7.8\n    },\n    {\n        id: \"nature_river\",\n        filename: \"river.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"River\",\n            zh: \"河流\"\n        },\n        description: {\n            en: \"Flowing river water for natural relaxation\",\n            zh: \"流淌的河水，自然放松\"\n        },\n        tags: [\n            \"flowing\",\n            \"natural\",\n            \"peaceful\"\n        ],\n        duration: 3600,\n        scientificRating: 9.1,\n        sleepEffectiveness: 9.4,\n        focusEffectiveness: 8.9\n    },\n    {\n        id: \"nature_walk_in_snow\",\n        filename: \"walk-in-snow.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Walk in Snow\",\n            zh: \"雪中漫步\"\n        },\n        description: {\n            en: \"Footsteps crunching in fresh snow\",\n            zh: \"脚步踩在新雪上的声音\"\n        },\n        tags: [\n            \"winter\",\n            \"crisp\",\n            \"peaceful\"\n        ],\n        duration: 3600,\n        scientificRating: 8.2,\n        sleepEffectiveness: 8.4,\n        focusEffectiveness: 8.0\n    },\n    {\n        id: \"nature_walk_on_gravel\",\n        filename: \"walk-on-gravel.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Walk on Gravel\",\n            zh: \"砾石路漫步\"\n        },\n        description: {\n            en: \"Footsteps on gravel path\",\n            zh: \"脚步声在砾石路上\"\n        },\n        tags: [\n            \"walking\",\n            \"texture\",\n            \"rhythmic\"\n        ],\n        duration: 3600,\n        scientificRating: 7.8,\n        sleepEffectiveness: 7.9,\n        focusEffectiveness: 7.5\n    },\n    {\n        id: \"nature_walk_on_leaves\",\n        filename: \"walk-on-leaves.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Walk on Leaves\",\n            zh: \"踏叶而行\"\n        },\n        description: {\n            en: \"Footsteps rustling through autumn leaves\",\n            zh: \"脚步踩过秋叶的沙沙声\"\n        },\n        tags: [\n            \"autumn\",\n            \"rustling\",\n            \"seasonal\"\n        ],\n        duration: 3600,\n        scientificRating: 8.3,\n        sleepEffectiveness: 8.5,\n        focusEffectiveness: 8.1\n    },\n    {\n        id: \"nature_waterfall\",\n        filename: \"waterfall.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Waterfall\",\n            zh: \"瀑布\"\n        },\n        description: {\n            en: \"Powerful waterfall cascading down rocks\",\n            zh: \"瀑布从岩石上倾泻而下\"\n        },\n        tags: [\n            \"powerful\",\n            \"cascading\",\n            \"majestic\"\n        ],\n        duration: 3600,\n        scientificRating: 8.9,\n        sleepEffectiveness: 8.7,\n        focusEffectiveness: 8.4\n    },\n    {\n        id: \"nature_waves\",\n        filename: \"waves.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Ocean Waves\",\n            zh: \"海浪\"\n        },\n        description: {\n            en: \"Gentle ocean waves lapping the shore\",\n            zh: \"轻柔的海浪拍打海岸\"\n        },\n        tags: [\n            \"ocean\",\n            \"rhythmic\",\n            \"calming\"\n        ],\n        duration: 3600,\n        scientificRating: 9.3,\n        sleepEffectiveness: 9.6,\n        focusEffectiveness: 9.0\n    },\n    {\n        id: \"nature_wind_in_trees\",\n        filename: \"wind-in-trees.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Wind in Trees\",\n            zh: \"林间风声\"\n        },\n        description: {\n            en: \"Wind rustling through tree branches\",\n            zh: \"风吹过树枝的沙沙声\"\n        },\n        tags: [\n            \"rustling\",\n            \"peaceful\",\n            \"forest\"\n        ],\n        duration: 3600,\n        scientificRating: 8.8,\n        sleepEffectiveness: 9.0,\n        focusEffectiveness: 8.6\n    },\n    {\n        id: \"nature_wind\",\n        filename: \"wind.mp3\",\n        category: \"nature\",\n        title: {\n            en: \"Wind\",\n            zh: \"风声\"\n        },\n        description: {\n            en: \"Gentle wind blowing across open spaces\",\n            zh: \"轻风吹过开阔地带\"\n        },\n        tags: [\n            \"gentle\",\n            \"open\",\n            \"airy\"\n        ],\n        duration: 3600,\n        scientificRating: 8.5,\n        sleepEffectiveness: 8.7,\n        focusEffectiveness: 8.3\n    },\n    // Noise Category - 匹配实际文件 (/Sounds/Noise/)\n    {\n        id: \"noise_brown_noise\",\n        filename: \"brown-noise.wav\",\n        category: \"noise\",\n        title: {\n            en: \"Brown Noise\",\n            zh: \"棕色噪音\"\n        },\n        description: {\n            en: \"Deep brown noise for focus and relaxation\",\n            zh: \"深沉的棕色噪音，专注与放松\"\n        },\n        tags: [\n            \"deep\",\n            \"focus\",\n            \"masking\"\n        ],\n        duration: 3600,\n        scientificRating: 9.0,\n        sleepEffectiveness: 8.8,\n        focusEffectiveness: 9.2\n    },\n    {\n        id: \"noise_pink_noise\",\n        filename: \"pink-noise.wav\",\n        category: \"noise\",\n        title: {\n            en: \"Pink Noise\",\n            zh: \"粉色噪音\"\n        },\n        description: {\n            en: \"Balanced pink noise for sleep and concentration\",\n            zh: \"平衡的粉色噪音，助眠与专注\"\n        },\n        tags: [\n            \"balanced\",\n            \"sleep\",\n            \"concentration\"\n        ],\n        duration: 3600,\n        scientificRating: 9.2,\n        sleepEffectiveness: 9.4,\n        focusEffectiveness: 9.1\n    },\n    {\n        id: \"noise_white_noise\",\n        filename: \"white-noise.wav\",\n        category: \"noise\",\n        title: {\n            en: \"White Noise\",\n            zh: \"白色噪音\"\n        },\n        description: {\n            en: \"Classic white noise for sound masking\",\n            zh: \"经典白色噪音，声音遮蔽\"\n        },\n        tags: [\n            \"classic\",\n            \"masking\",\n            \"consistent\"\n        ],\n        duration: 3600,\n        scientificRating: 8.9,\n        sleepEffectiveness: 9.0,\n        focusEffectiveness: 9.3\n    },\n    // Animals Category - 匹配实际文件 (/Sounds/Animals/)\n    {\n        id: \"animals_beehive\",\n        filename: \"beehive.mp3\",\n        category: \"animals\",\n        title: {\n            en: \"Beehive\",\n            zh: \"蜂巢\"\n        },\n        description: {\n            en: \"Gentle buzzing of bees in their hive\",\n            zh: \"蜜蜂在蜂巢中轻柔的嗡嗡声\"\n        },\n        tags: [\n            \"buzzing\",\n            \"gentle\",\n            \"productive\"\n        ],\n        duration: 3600,\n        scientificRating: 8.1,\n        sleepEffectiveness: 8.3,\n        focusEffectiveness: 8.5\n    },\n    {\n        id: \"animals_birds\",\n        filename: \"birds.mp3\",\n        category: \"animals\",\n        title: {\n            en: \"Birds\",\n            zh: \"鸟鸣\"\n        },\n        description: {\n            en: \"Cheerful bird songs for morning atmosphere\",\n            zh: \"欢快的鸟鸣声，晨间氛围\"\n        },\n        tags: [\n            \"cheerful\",\n            \"morning\",\n            \"natural\"\n        ],\n        duration: 3600,\n        scientificRating: 8.7,\n        sleepEffectiveness: 8.2,\n        focusEffectiveness: 8.8\n    },\n    {\n        id: \"animals_cat_purring\",\n        filename: \"cat-purring.mp3\",\n        category: \"animals\",\n        title: {\n            en: \"Cat Purring\",\n            zh: \"猫咪呼噜声\"\n        },\n        description: {\n            en: \"Soothing cat purring for comfort and relaxation\",\n            zh: \"舒缓的猫咪呼噜声，带来安慰与放松\"\n        },\n        tags: [\n            \"soothing\",\n            \"comfort\",\n            \"cozy\"\n        ],\n        duration: 3600,\n        scientificRating: 8.9,\n        sleepEffectiveness: 9.1,\n        focusEffectiveness: 8.4\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/audioData.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAudioPlayer.ts":
/*!*************************************!*\
  !*** ./src/hooks/useAudioPlayer.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioPlayer: () => (/* binding */ useAudioPlayer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! howler */ \"(ssr)/./node_modules/howler/dist/howler.js\");\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(howler__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/audioStore */ \"(ssr)/./src/store/audioStore.ts\");\n\n\n\nconst useAudioPlayer = ()=>{\n    const howlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { currentSound, playState, userVolume, setCurrentSound, updatePlayState, setUserVolume } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore)();\n    // 清理定时器\n    const clearProgressInterval = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n        }\n    }, []);\n    // 开始进度更新定时器\n    const startProgressInterval = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        clearProgressInterval();\n        intervalRef.current = setInterval(()=>{\n            if (howlRef.current && howlRef.current.playing()) {\n                const currentTime = howlRef.current.seek();\n                updatePlayState({\n                    currentTime\n                });\n            }\n        }, 1000);\n    }, [\n        updatePlayState,\n        clearProgressInterval\n    ]);\n    // 播放音频\n    const play = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((sound)=>{\n        try {\n            // 如果传入了新的音频，先停止当前播放\n            if (sound && sound.id !== currentSound?.id) {\n                stop();\n                setCurrentSound(sound);\n                // 创建新的 Howl 实例 - 映射小写分类到大写文件夹\n                const categoryMap = {\n                    \"rain\": \"Rain\",\n                    \"nature\": \"Nature\",\n                    \"noise\": \"Noise\",\n                    \"animals\": \"Animals\",\n                    \"things\": \"Things\",\n                    \"transport\": \"Transport\",\n                    \"urban\": \"Urban\",\n                    \"places\": \"Places\",\n                    \"ocean\": \"Ocean\"\n                };\n                const folderName = categoryMap[sound.category] || sound.category;\n                const audioUrl = `/Sounds/${folderName}/${sound.filename}`;\n                howlRef.current = new howler__WEBPACK_IMPORTED_MODULE_1__.Howl({\n                    src: [\n                        audioUrl\n                    ],\n                    volume: userVolume,\n                    loop: playState.isLooping,\n                    onload: ()=>{\n                        updatePlayState({\n                            isLoading: false,\n                            duration: howlRef.current?.duration() || 0,\n                            error: undefined\n                        });\n                    },\n                    onplay: ()=>{\n                        updatePlayState({\n                            isPlaying: true,\n                            isPaused: false,\n                            error: undefined\n                        });\n                        startProgressInterval();\n                    },\n                    onpause: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: true\n                        });\n                        clearProgressInterval();\n                    },\n                    onstop: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: false,\n                            currentTime: 0\n                        });\n                        clearProgressInterval();\n                    },\n                    onend: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: false,\n                            currentTime: 0\n                        });\n                        clearProgressInterval();\n                    },\n                    onloaderror: (id, error)=>{\n                        console.error(\"音频加载失败:\", error);\n                        updatePlayState({\n                            isLoading: false,\n                            error: \"音频加载失败\"\n                        });\n                    },\n                    onplayerror: (id, error)=>{\n                        console.error(\"音频播放失败:\", error);\n                        updatePlayState({\n                            isPlaying: false,\n                            error: \"音频播放失败\"\n                        });\n                    }\n                });\n                updatePlayState({\n                    isLoading: true\n                });\n            }\n            // 播放音频\n            if (howlRef.current) {\n                if (playState.isPaused) {\n                    howlRef.current.play();\n                } else {\n                    howlRef.current.play();\n                }\n            }\n        } catch (error) {\n            console.error(\"播放音频时发生错误:\", error);\n            updatePlayState({\n                error: \"播放失败\",\n                isLoading: false\n            });\n        }\n    }, [\n        currentSound,\n        playState.isLooping,\n        playState.isPaused,\n        userVolume,\n        setCurrentSound,\n        updatePlayState,\n        startProgressInterval\n    ]);\n    // 暂停音频\n    const pause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (howlRef.current && howlRef.current.playing()) {\n            howlRef.current.pause();\n        }\n    }, []);\n    // 停止音频\n    const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (howlRef.current) {\n            howlRef.current.stop();\n            howlRef.current.unload();\n            howlRef.current = null;\n        }\n        clearProgressInterval();\n        updatePlayState({\n            isPlaying: false,\n            isPaused: false,\n            currentTime: 0\n        });\n    }, [\n        clearProgressInterval,\n        updatePlayState\n    ]);\n    // 设置音量\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((volume)=>{\n        const clampedVolume = Math.max(0, Math.min(1, volume));\n        setUserVolume(clampedVolume);\n        updatePlayState({\n            volume: clampedVolume\n        });\n        if (howlRef.current) {\n            howlRef.current.volume(clampedVolume);\n        }\n        // 设置全局音量\n        howler__WEBPACK_IMPORTED_MODULE_1__.Howler.volume(clampedVolume);\n    }, [\n        setUserVolume,\n        updatePlayState\n    ]);\n    // 设置循环播放\n    const setLoop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((loop)=>{\n        updatePlayState({\n            isLooping: loop\n        });\n        if (howlRef.current) {\n            howlRef.current.loop(loop);\n        }\n    }, [\n        updatePlayState\n    ]);\n    // 跳转到指定位置\n    const seek = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((position)=>{\n        if (howlRef.current) {\n            howlRef.current.seek(position);\n            updatePlayState({\n                currentTime: position\n            });\n        }\n    }, [\n        updatePlayState\n    ]);\n    // 组件卸载时清理资源\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            stop();\n        };\n    }, [\n        stop\n    ]);\n    // 监听音量变化\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (howlRef.current && playState.volume !== userVolume) {\n            howlRef.current.volume(userVolume);\n        }\n    }, [\n        userVolume,\n        playState.volume\n    ]);\n    return {\n        play,\n        pause,\n        stop,\n        setVolume,\n        setLoop,\n        seek,\n        isPlaying: playState.isPlaying,\n        isPaused: playState.isPaused,\n        isLoading: playState.isLoading,\n        currentTime: playState.currentTime,\n        duration: playState.duration,\n        volume: playState.volume,\n        isLooping: playState.isLooping,\n        error: playState.error || null\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlQXVkaW9QbGF5ZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXVEO0FBQ2pCO0FBQ2E7QUFvQjVDLE1BQU1NLGlCQUFpQjtJQUM1QixNQUFNQyxVQUFVTiw2Q0FBTUEsQ0FBYztJQUNwQyxNQUFNTyxjQUFjUCw2Q0FBTUEsQ0FBd0I7SUFFbEQsTUFBTSxFQUNKUSxZQUFZLEVBQ1pDLFNBQVMsRUFDVEMsVUFBVSxFQUNWQyxlQUFlLEVBQ2ZDLGVBQWUsRUFDZkMsYUFBYSxFQUNkLEdBQUdULGdFQUFhQTtJQUVqQixRQUFRO0lBQ1IsTUFBTVUsd0JBQXdCYixrREFBV0EsQ0FBQztRQUN4QyxJQUFJTSxZQUFZUSxPQUFPLEVBQUU7WUFDdkJDLGNBQWNULFlBQVlRLE9BQU87WUFDakNSLFlBQVlRLE9BQU8sR0FBRztRQUN4QjtJQUNGLEdBQUcsRUFBRTtJQUVMLFlBQVk7SUFDWixNQUFNRSx3QkFBd0JoQixrREFBV0EsQ0FBQztRQUN4Q2E7UUFDQVAsWUFBWVEsT0FBTyxHQUFHRyxZQUFZO1lBQ2hDLElBQUlaLFFBQVFTLE9BQU8sSUFBSVQsUUFBUVMsT0FBTyxDQUFDSSxPQUFPLElBQUk7Z0JBQ2hELE1BQU1DLGNBQWNkLFFBQVFTLE9BQU8sQ0FBQ00sSUFBSTtnQkFDeENULGdCQUFnQjtvQkFBRVE7Z0JBQVk7WUFDaEM7UUFDRixHQUFHO0lBQ0wsR0FBRztRQUFDUjtRQUFpQkU7S0FBc0I7SUFFM0MsT0FBTztJQUNQLE1BQU1RLE9BQU9yQixrREFBV0EsQ0FBQyxDQUFDc0I7UUFDeEIsSUFBSTtZQUNGLG9CQUFvQjtZQUNwQixJQUFJQSxTQUFTQSxNQUFNQyxFQUFFLEtBQUtoQixjQUFjZ0IsSUFBSTtnQkFDMUNDO2dCQUNBZCxnQkFBZ0JZO2dCQUVoQiw4QkFBOEI7Z0JBQzlCLE1BQU1HLGNBQXNDO29CQUMxQyxRQUFRO29CQUNSLFVBQVU7b0JBQ1YsU0FBUztvQkFDVCxXQUFXO29CQUNYLFVBQVU7b0JBQ1YsYUFBYTtvQkFDYixTQUFTO29CQUNULFVBQVU7b0JBQ1YsU0FBUztnQkFDWDtnQkFDQSxNQUFNQyxhQUFhRCxXQUFXLENBQUNILE1BQU1LLFFBQVEsQ0FBQyxJQUFJTCxNQUFNSyxRQUFRO2dCQUNoRSxNQUFNQyxXQUFXLENBQUMsUUFBUSxFQUFFRixXQUFXLENBQUMsRUFBRUosTUFBTU8sUUFBUSxDQUFDLENBQUM7Z0JBQzFEeEIsUUFBUVMsT0FBTyxHQUFHLElBQUliLHdDQUFJQSxDQUFDO29CQUN6QjZCLEtBQUs7d0JBQUNGO3FCQUFTO29CQUNmRyxRQUFRdEI7b0JBQ1J1QixNQUFNeEIsVUFBVXlCLFNBQVM7b0JBQ3pCQyxRQUFRO3dCQUNOdkIsZ0JBQWdCOzRCQUNkd0IsV0FBVzs0QkFDWEMsVUFBVS9CLFFBQVFTLE9BQU8sRUFBRXNCLGNBQWM7NEJBQ3pDQyxPQUFPQzt3QkFDVDtvQkFDRjtvQkFDQUMsUUFBUTt3QkFDTjVCLGdCQUFnQjs0QkFDZDZCLFdBQVc7NEJBQ1hDLFVBQVU7NEJBQ1ZKLE9BQU9DO3dCQUNUO3dCQUNBdEI7b0JBQ0Y7b0JBQ0EwQixTQUFTO3dCQUNQL0IsZ0JBQWdCOzRCQUNkNkIsV0FBVzs0QkFDWEMsVUFBVTt3QkFDWjt3QkFDQTVCO29CQUNGO29CQUNBOEIsUUFBUTt3QkFDTmhDLGdCQUFnQjs0QkFDZDZCLFdBQVc7NEJBQ1hDLFVBQVU7NEJBQ1Z0QixhQUFhO3dCQUNmO3dCQUNBTjtvQkFDRjtvQkFDQStCLE9BQU87d0JBQ0xqQyxnQkFBZ0I7NEJBQ2Q2QixXQUFXOzRCQUNYQyxVQUFVOzRCQUNWdEIsYUFBYTt3QkFDZjt3QkFDQU47b0JBQ0Y7b0JBQ0FnQyxhQUFhLENBQUN0QixJQUFJYzt3QkFDaEJTLFFBQVFULEtBQUssQ0FBQyxXQUFXQTt3QkFDekIxQixnQkFBZ0I7NEJBQ2R3QixXQUFXOzRCQUNYRSxPQUFPO3dCQUNUO29CQUNGO29CQUNBVSxhQUFhLENBQUN4QixJQUFJYzt3QkFDaEJTLFFBQVFULEtBQUssQ0FBQyxXQUFXQTt3QkFDekIxQixnQkFBZ0I7NEJBQ2Q2QixXQUFXOzRCQUNYSCxPQUFPO3dCQUNUO29CQUNGO2dCQUNGO2dCQUVBMUIsZ0JBQWdCO29CQUFFd0IsV0FBVztnQkFBSztZQUNwQztZQUVBLE9BQU87WUFDUCxJQUFJOUIsUUFBUVMsT0FBTyxFQUFFO2dCQUNuQixJQUFJTixVQUFVaUMsUUFBUSxFQUFFO29CQUN0QnBDLFFBQVFTLE9BQU8sQ0FBQ08sSUFBSTtnQkFDdEIsT0FBTztvQkFDTGhCLFFBQVFTLE9BQU8sQ0FBQ08sSUFBSTtnQkFDdEI7WUFDRjtRQUNGLEVBQUUsT0FBT2dCLE9BQU87WUFDZFMsUUFBUVQsS0FBSyxDQUFDLGNBQWNBO1lBQzVCMUIsZ0JBQWdCO2dCQUNkMEIsT0FBTztnQkFDUEYsV0FBVztZQUNiO1FBQ0Y7SUFDRixHQUFHO1FBQUM1QjtRQUFjQyxVQUFVeUIsU0FBUztRQUFFekIsVUFBVWlDLFFBQVE7UUFBRWhDO1FBQVlDO1FBQWlCQztRQUFpQks7S0FBc0I7SUFFL0gsT0FBTztJQUNQLE1BQU1nQyxRQUFRaEQsa0RBQVdBLENBQUM7UUFDeEIsSUFBSUssUUFBUVMsT0FBTyxJQUFJVCxRQUFRUyxPQUFPLENBQUNJLE9BQU8sSUFBSTtZQUNoRGIsUUFBUVMsT0FBTyxDQUFDa0MsS0FBSztRQUN2QjtJQUNGLEdBQUcsRUFBRTtJQUVMLE9BQU87SUFDUCxNQUFNeEIsT0FBT3hCLGtEQUFXQSxDQUFDO1FBQ3ZCLElBQUlLLFFBQVFTLE9BQU8sRUFBRTtZQUNuQlQsUUFBUVMsT0FBTyxDQUFDVSxJQUFJO1lBQ3BCbkIsUUFBUVMsT0FBTyxDQUFDbUMsTUFBTTtZQUN0QjVDLFFBQVFTLE9BQU8sR0FBRztRQUNwQjtRQUNBRDtRQUNBRixnQkFBZ0I7WUFDZDZCLFdBQVc7WUFDWEMsVUFBVTtZQUNWdEIsYUFBYTtRQUNmO0lBQ0YsR0FBRztRQUFDTjtRQUF1QkY7S0FBZ0I7SUFFM0MsT0FBTztJQUNQLE1BQU11QyxZQUFZbEQsa0RBQVdBLENBQUMsQ0FBQytCO1FBQzdCLE1BQU1vQixnQkFBZ0JDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHRCxLQUFLRSxHQUFHLENBQUMsR0FBR3ZCO1FBQzlDbkIsY0FBY3VDO1FBQ2R4QyxnQkFBZ0I7WUFBRW9CLFFBQVFvQjtRQUFjO1FBRXhDLElBQUk5QyxRQUFRUyxPQUFPLEVBQUU7WUFDbkJULFFBQVFTLE9BQU8sQ0FBQ2lCLE1BQU0sQ0FBQ29CO1FBQ3pCO1FBRUEsU0FBUztRQUNUakQsMENBQU1BLENBQUM2QixNQUFNLENBQUNvQjtJQUNoQixHQUFHO1FBQUN2QztRQUFlRDtLQUFnQjtJQUVuQyxTQUFTO0lBQ1QsTUFBTTRDLFVBQVV2RCxrREFBV0EsQ0FBQyxDQUFDZ0M7UUFDM0JyQixnQkFBZ0I7WUFBRXNCLFdBQVdEO1FBQUs7UUFDbEMsSUFBSTNCLFFBQVFTLE9BQU8sRUFBRTtZQUNuQlQsUUFBUVMsT0FBTyxDQUFDa0IsSUFBSSxDQUFDQTtRQUN2QjtJQUNGLEdBQUc7UUFBQ3JCO0tBQWdCO0lBRXBCLFVBQVU7SUFDVixNQUFNUyxPQUFPcEIsa0RBQVdBLENBQUMsQ0FBQ3dEO1FBQ3hCLElBQUluRCxRQUFRUyxPQUFPLEVBQUU7WUFDbkJULFFBQVFTLE9BQU8sQ0FBQ00sSUFBSSxDQUFDb0M7WUFDckI3QyxnQkFBZ0I7Z0JBQUVRLGFBQWFxQztZQUFTO1FBQzFDO0lBQ0YsR0FBRztRQUFDN0M7S0FBZ0I7SUFFcEIsWUFBWTtJQUNaYixnREFBU0EsQ0FBQztRQUNSLE9BQU87WUFDTDBCO1FBQ0Y7SUFDRixHQUFHO1FBQUNBO0tBQUs7SUFFVCxTQUFTO0lBQ1QxQixnREFBU0EsQ0FBQztRQUNSLElBQUlPLFFBQVFTLE9BQU8sSUFBSU4sVUFBVXVCLE1BQU0sS0FBS3RCLFlBQVk7WUFDdERKLFFBQVFTLE9BQU8sQ0FBQ2lCLE1BQU0sQ0FBQ3RCO1FBQ3pCO0lBQ0YsR0FBRztRQUFDQTtRQUFZRCxVQUFVdUIsTUFBTTtLQUFDO0lBRWpDLE9BQU87UUFDTFY7UUFDQTJCO1FBQ0F4QjtRQUNBMEI7UUFDQUs7UUFDQW5DO1FBQ0FvQixXQUFXaEMsVUFBVWdDLFNBQVM7UUFDOUJDLFVBQVVqQyxVQUFVaUMsUUFBUTtRQUM1Qk4sV0FBVzNCLFVBQVUyQixTQUFTO1FBQzlCaEIsYUFBYVgsVUFBVVcsV0FBVztRQUNsQ2lCLFVBQVU1QixVQUFVNEIsUUFBUTtRQUM1QkwsUUFBUXZCLFVBQVV1QixNQUFNO1FBQ3hCRSxXQUFXekIsVUFBVXlCLFNBQVM7UUFDOUJJLE9BQU83QixVQUFVNkIsS0FBSyxJQUFJO0lBQzVCO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2hvb2tzL3VzZUF1ZGlvUGxheWVyLnRzPzY5NTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgSG93bCwgSG93bGVyIH0gZnJvbSAnaG93bGVyJztcbmltcG9ydCB7IHVzZUF1ZGlvU3RvcmUgfSBmcm9tICdAL3N0b3JlL2F1ZGlvU3RvcmUnO1xuaW1wb3J0IHsgTXVsdGlsaW5ndWFsQXVkaW9JdGVtIH0gZnJvbSAnQC90eXBlcy9hdWRpbyc7XG5cbmludGVyZmFjZSBVc2VBdWRpb1BsYXllclJldHVybiB7XG4gIHBsYXk6IChzb3VuZD86IE11bHRpbGluZ3VhbEF1ZGlvSXRlbSkgPT4gdm9pZDtcbiAgcGF1c2U6ICgpID0+IHZvaWQ7XG4gIHN0b3A6ICgpID0+IHZvaWQ7XG4gIHNldFZvbHVtZTogKHZvbHVtZTogbnVtYmVyKSA9PiB2b2lkO1xuICBzZXRMb29wOiAobG9vcDogYm9vbGVhbikgPT4gdm9pZDtcbiAgc2VlazogKHBvc2l0aW9uOiBudW1iZXIpID0+IHZvaWQ7XG4gIGlzUGxheWluZzogYm9vbGVhbjtcbiAgaXNQYXVzZWQ6IGJvb2xlYW47XG4gIGlzTG9hZGluZzogYm9vbGVhbjtcbiAgY3VycmVudFRpbWU6IG51bWJlcjtcbiAgZHVyYXRpb246IG51bWJlcjtcbiAgdm9sdW1lOiBudW1iZXI7XG4gIGlzTG9vcGluZzogYm9vbGVhbjtcbiAgZXJyb3I6IHN0cmluZyB8IG51bGw7XG59XG5cbmV4cG9ydCBjb25zdCB1c2VBdWRpb1BsYXllciA9ICgpOiBVc2VBdWRpb1BsYXllclJldHVybiA9PiB7XG4gIGNvbnN0IGhvd2xSZWYgPSB1c2VSZWY8SG93bCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBpbnRlcnZhbFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xuICBcbiAgY29uc3Qge1xuICAgIGN1cnJlbnRTb3VuZCxcbiAgICBwbGF5U3RhdGUsXG4gICAgdXNlclZvbHVtZSxcbiAgICBzZXRDdXJyZW50U291bmQsXG4gICAgdXBkYXRlUGxheVN0YXRlLFxuICAgIHNldFVzZXJWb2x1bWUsXG4gIH0gPSB1c2VBdWRpb1N0b3JlKCk7XG5cbiAgLy8g5riF55CG5a6a5pe25ZmoXG4gIGNvbnN0IGNsZWFyUHJvZ3Jlc3NJbnRlcnZhbCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoaW50ZXJ2YWxSZWYuY3VycmVudCkge1xuICAgICAgY2xlYXJJbnRlcnZhbChpbnRlcnZhbFJlZi5jdXJyZW50KTtcbiAgICAgIGludGVydmFsUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vIOW8gOWni+i/m+W6puabtOaWsOWumuaXtuWZqFxuICBjb25zdCBzdGFydFByb2dyZXNzSW50ZXJ2YWwgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgY2xlYXJQcm9ncmVzc0ludGVydmFsKCk7XG4gICAgaW50ZXJ2YWxSZWYuY3VycmVudCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIGlmIChob3dsUmVmLmN1cnJlbnQgJiYgaG93bFJlZi5jdXJyZW50LnBsYXlpbmcoKSkge1xuICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IGhvd2xSZWYuY3VycmVudC5zZWVrKCkgYXMgbnVtYmVyO1xuICAgICAgICB1cGRhdGVQbGF5U3RhdGUoeyBjdXJyZW50VGltZSB9KTtcbiAgICAgIH1cbiAgICB9LCAxMDAwKTtcbiAgfSwgW3VwZGF0ZVBsYXlTdGF0ZSwgY2xlYXJQcm9ncmVzc0ludGVydmFsXSk7XG5cbiAgLy8g5pKt5pS+6Z+z6aKRXG4gIGNvbnN0IHBsYXkgPSB1c2VDYWxsYmFjaygoc291bmQ/OiBNdWx0aWxpbmd1YWxBdWRpb0l0ZW0pID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g5aaC5p6c5Lyg5YWl5LqG5paw55qE6Z+z6aKR77yM5YWI5YGc5q2i5b2T5YmN5pKt5pS+XG4gICAgICBpZiAoc291bmQgJiYgc291bmQuaWQgIT09IGN1cnJlbnRTb3VuZD8uaWQpIHtcbiAgICAgICAgc3RvcCgpO1xuICAgICAgICBzZXRDdXJyZW50U291bmQoc291bmQpO1xuICAgICAgICBcbiAgICAgICAgLy8g5Yib5bu65paw55qEIEhvd2wg5a6e5L6LIC0g5pig5bCE5bCP5YaZ5YiG57G75Yiw5aSn5YaZ5paH5Lu25aS5XG4gICAgICAgIGNvbnN0IGNhdGVnb3J5TWFwOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xuICAgICAgICAgICdyYWluJzogJ1JhaW4nLFxuICAgICAgICAgICduYXR1cmUnOiAnTmF0dXJlJyxcbiAgICAgICAgICAnbm9pc2UnOiAnTm9pc2UnLFxuICAgICAgICAgICdhbmltYWxzJzogJ0FuaW1hbHMnLFxuICAgICAgICAgICd0aGluZ3MnOiAnVGhpbmdzJyxcbiAgICAgICAgICAndHJhbnNwb3J0JzogJ1RyYW5zcG9ydCcsXG4gICAgICAgICAgJ3VyYmFuJzogJ1VyYmFuJyxcbiAgICAgICAgICAncGxhY2VzJzogJ1BsYWNlcycsXG4gICAgICAgICAgJ29jZWFuJzogJ09jZWFuJ1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBmb2xkZXJOYW1lID0gY2F0ZWdvcnlNYXBbc291bmQuY2F0ZWdvcnldIHx8IHNvdW5kLmNhdGVnb3J5O1xuICAgICAgICBjb25zdCBhdWRpb1VybCA9IGAvU291bmRzLyR7Zm9sZGVyTmFtZX0vJHtzb3VuZC5maWxlbmFtZX1gO1xuICAgICAgICBob3dsUmVmLmN1cnJlbnQgPSBuZXcgSG93bCh7XG4gICAgICAgICAgc3JjOiBbYXVkaW9VcmxdLFxuICAgICAgICAgIHZvbHVtZTogdXNlclZvbHVtZSxcbiAgICAgICAgICBsb29wOiBwbGF5U3RhdGUuaXNMb29waW5nLFxuICAgICAgICAgIG9ubG9hZDogKCkgPT4ge1xuICAgICAgICAgICAgdXBkYXRlUGxheVN0YXRlKHtcbiAgICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgZHVyYXRpb246IGhvd2xSZWYuY3VycmVudD8uZHVyYXRpb24oKSB8fCAwLFxuICAgICAgICAgICAgICBlcnJvcjogdW5kZWZpbmVkLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBvbnBsYXk6ICgpID0+IHtcbiAgICAgICAgICAgIHVwZGF0ZVBsYXlTdGF0ZSh7XG4gICAgICAgICAgICAgIGlzUGxheWluZzogdHJ1ZSxcbiAgICAgICAgICAgICAgaXNQYXVzZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICBlcnJvcjogdW5kZWZpbmVkLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBzdGFydFByb2dyZXNzSW50ZXJ2YWwoKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIG9ucGF1c2U6ICgpID0+IHtcbiAgICAgICAgICAgIHVwZGF0ZVBsYXlTdGF0ZSh7XG4gICAgICAgICAgICAgIGlzUGxheWluZzogZmFsc2UsXG4gICAgICAgICAgICAgIGlzUGF1c2VkOiB0cnVlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjbGVhclByb2dyZXNzSW50ZXJ2YWwoKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIG9uc3RvcDogKCkgPT4ge1xuICAgICAgICAgICAgdXBkYXRlUGxheVN0YXRlKHtcbiAgICAgICAgICAgICAgaXNQbGF5aW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgaXNQYXVzZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICBjdXJyZW50VGltZTogMCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY2xlYXJQcm9ncmVzc0ludGVydmFsKCk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBvbmVuZDogKCkgPT4ge1xuICAgICAgICAgICAgdXBkYXRlUGxheVN0YXRlKHtcbiAgICAgICAgICAgICAgaXNQbGF5aW5nOiBmYWxzZSxcbiAgICAgICAgICAgICAgaXNQYXVzZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICBjdXJyZW50VGltZTogMCxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgY2xlYXJQcm9ncmVzc0ludGVydmFsKCk7XG4gICAgICAgICAgfSxcbiAgICAgICAgICBvbmxvYWRlcnJvcjogKGlkLCBlcnJvcikgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign6Z+z6aKR5Yqg6L295aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgICAgIHVwZGF0ZVBsYXlTdGF0ZSh7XG4gICAgICAgICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICAgICAgICAgIGVycm9yOiAn6Z+z6aKR5Yqg6L295aSx6LSlJyxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25wbGF5ZXJyb3I6IChpZCwgZXJyb3IpID0+IHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+mfs+mikeaSreaUvuWksei0pTonLCBlcnJvcik7XG4gICAgICAgICAgICB1cGRhdGVQbGF5U3RhdGUoe1xuICAgICAgICAgICAgICBpc1BsYXlpbmc6IGZhbHNlLFxuICAgICAgICAgICAgICBlcnJvcjogJ+mfs+mikeaSreaUvuWksei0pScsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICB9LFxuICAgICAgICB9KTtcblxuICAgICAgICB1cGRhdGVQbGF5U3RhdGUoeyBpc0xvYWRpbmc6IHRydWUgfSk7XG4gICAgICB9XG5cbiAgICAgIC8vIOaSreaUvumfs+mikVxuICAgICAgaWYgKGhvd2xSZWYuY3VycmVudCkge1xuICAgICAgICBpZiAocGxheVN0YXRlLmlzUGF1c2VkKSB7XG4gICAgICAgICAgaG93bFJlZi5jdXJyZW50LnBsYXkoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBob3dsUmVmLmN1cnJlbnQucGxheSgpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aSreaUvumfs+mikeaXtuWPkeeUn+mUmeivrzonLCBlcnJvcik7XG4gICAgICB1cGRhdGVQbGF5U3RhdGUoe1xuICAgICAgICBlcnJvcjogJ+aSreaUvuWksei0pScsXG4gICAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICB9KTtcbiAgICB9XG4gIH0sIFtjdXJyZW50U291bmQsIHBsYXlTdGF0ZS5pc0xvb3BpbmcsIHBsYXlTdGF0ZS5pc1BhdXNlZCwgdXNlclZvbHVtZSwgc2V0Q3VycmVudFNvdW5kLCB1cGRhdGVQbGF5U3RhdGUsIHN0YXJ0UHJvZ3Jlc3NJbnRlcnZhbF0pO1xuXG4gIC8vIOaaguWBnOmfs+mikVxuICBjb25zdCBwYXVzZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBpZiAoaG93bFJlZi5jdXJyZW50ICYmIGhvd2xSZWYuY3VycmVudC5wbGF5aW5nKCkpIHtcbiAgICAgIGhvd2xSZWYuY3VycmVudC5wYXVzZSgpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8vIOWBnOatoumfs+mikVxuICBjb25zdCBzdG9wID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChob3dsUmVmLmN1cnJlbnQpIHtcbiAgICAgIGhvd2xSZWYuY3VycmVudC5zdG9wKCk7XG4gICAgICBob3dsUmVmLmN1cnJlbnQudW5sb2FkKCk7XG4gICAgICBob3dsUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cbiAgICBjbGVhclByb2dyZXNzSW50ZXJ2YWwoKTtcbiAgICB1cGRhdGVQbGF5U3RhdGUoe1xuICAgICAgaXNQbGF5aW5nOiBmYWxzZSxcbiAgICAgIGlzUGF1c2VkOiBmYWxzZSxcbiAgICAgIGN1cnJlbnRUaW1lOiAwLFxuICAgIH0pO1xuICB9LCBbY2xlYXJQcm9ncmVzc0ludGVydmFsLCB1cGRhdGVQbGF5U3RhdGVdKTtcblxuICAvLyDorr7nva7pn7Pph49cbiAgY29uc3Qgc2V0Vm9sdW1lID0gdXNlQ2FsbGJhY2soKHZvbHVtZTogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgY2xhbXBlZFZvbHVtZSA9IE1hdGgubWF4KDAsIE1hdGgubWluKDEsIHZvbHVtZSkpO1xuICAgIHNldFVzZXJWb2x1bWUoY2xhbXBlZFZvbHVtZSk7XG4gICAgdXBkYXRlUGxheVN0YXRlKHsgdm9sdW1lOiBjbGFtcGVkVm9sdW1lIH0pO1xuICAgIFxuICAgIGlmIChob3dsUmVmLmN1cnJlbnQpIHtcbiAgICAgIGhvd2xSZWYuY3VycmVudC52b2x1bWUoY2xhbXBlZFZvbHVtZSk7XG4gICAgfVxuICAgIFxuICAgIC8vIOiuvue9ruWFqOWxgOmfs+mHj1xuICAgIEhvd2xlci52b2x1bWUoY2xhbXBlZFZvbHVtZSk7XG4gIH0sIFtzZXRVc2VyVm9sdW1lLCB1cGRhdGVQbGF5U3RhdGVdKTtcblxuICAvLyDorr7nva7lvqrnjq/mkq3mlL5cbiAgY29uc3Qgc2V0TG9vcCA9IHVzZUNhbGxiYWNrKChsb29wOiBib29sZWFuKSA9PiB7XG4gICAgdXBkYXRlUGxheVN0YXRlKHsgaXNMb29waW5nOiBsb29wIH0pO1xuICAgIGlmIChob3dsUmVmLmN1cnJlbnQpIHtcbiAgICAgIGhvd2xSZWYuY3VycmVudC5sb29wKGxvb3ApO1xuICAgIH1cbiAgfSwgW3VwZGF0ZVBsYXlTdGF0ZV0pO1xuXG4gIC8vIOi3s+i9rOWIsOaMh+WumuS9jee9rlxuICBjb25zdCBzZWVrID0gdXNlQ2FsbGJhY2soKHBvc2l0aW9uOiBudW1iZXIpID0+IHtcbiAgICBpZiAoaG93bFJlZi5jdXJyZW50KSB7XG4gICAgICBob3dsUmVmLmN1cnJlbnQuc2Vlayhwb3NpdGlvbik7XG4gICAgICB1cGRhdGVQbGF5U3RhdGUoeyBjdXJyZW50VGltZTogcG9zaXRpb24gfSk7XG4gICAgfVxuICB9LCBbdXBkYXRlUGxheVN0YXRlXSk7XG5cbiAgLy8g57uE5Lu25Y246L295pe25riF55CG6LWE5rqQXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHN0b3AoKTtcbiAgICB9O1xuICB9LCBbc3RvcF0pO1xuXG4gIC8vIOebkeWQrOmfs+mHj+WPmOWMllxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChob3dsUmVmLmN1cnJlbnQgJiYgcGxheVN0YXRlLnZvbHVtZSAhPT0gdXNlclZvbHVtZSkge1xuICAgICAgaG93bFJlZi5jdXJyZW50LnZvbHVtZSh1c2VyVm9sdW1lKTtcbiAgICB9XG4gIH0sIFt1c2VyVm9sdW1lLCBwbGF5U3RhdGUudm9sdW1lXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBwbGF5LFxuICAgIHBhdXNlLFxuICAgIHN0b3AsXG4gICAgc2V0Vm9sdW1lLFxuICAgIHNldExvb3AsXG4gICAgc2VlayxcbiAgICBpc1BsYXlpbmc6IHBsYXlTdGF0ZS5pc1BsYXlpbmcsXG4gICAgaXNQYXVzZWQ6IHBsYXlTdGF0ZS5pc1BhdXNlZCxcbiAgICBpc0xvYWRpbmc6IHBsYXlTdGF0ZS5pc0xvYWRpbmcsXG4gICAgY3VycmVudFRpbWU6IHBsYXlTdGF0ZS5jdXJyZW50VGltZSxcbiAgICBkdXJhdGlvbjogcGxheVN0YXRlLmR1cmF0aW9uLFxuICAgIHZvbHVtZTogcGxheVN0YXRlLnZvbHVtZSxcbiAgICBpc0xvb3Bpbmc6IHBsYXlTdGF0ZS5pc0xvb3BpbmcsXG4gICAgZXJyb3I6IHBsYXlTdGF0ZS5lcnJvciB8fCBudWxsLFxuICB9O1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VDYWxsYmFjayIsIkhvd2wiLCJIb3dsZXIiLCJ1c2VBdWRpb1N0b3JlIiwidXNlQXVkaW9QbGF5ZXIiLCJob3dsUmVmIiwiaW50ZXJ2YWxSZWYiLCJjdXJyZW50U291bmQiLCJwbGF5U3RhdGUiLCJ1c2VyVm9sdW1lIiwic2V0Q3VycmVudFNvdW5kIiwidXBkYXRlUGxheVN0YXRlIiwic2V0VXNlclZvbHVtZSIsImNsZWFyUHJvZ3Jlc3NJbnRlcnZhbCIsImN1cnJlbnQiLCJjbGVhckludGVydmFsIiwic3RhcnRQcm9ncmVzc0ludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwbGF5aW5nIiwiY3VycmVudFRpbWUiLCJzZWVrIiwicGxheSIsInNvdW5kIiwiaWQiLCJzdG9wIiwiY2F0ZWdvcnlNYXAiLCJmb2xkZXJOYW1lIiwiY2F0ZWdvcnkiLCJhdWRpb1VybCIsImZpbGVuYW1lIiwic3JjIiwidm9sdW1lIiwibG9vcCIsImlzTG9vcGluZyIsIm9ubG9hZCIsImlzTG9hZGluZyIsImR1cmF0aW9uIiwiZXJyb3IiLCJ1bmRlZmluZWQiLCJvbnBsYXkiLCJpc1BsYXlpbmciLCJpc1BhdXNlZCIsIm9ucGF1c2UiLCJvbnN0b3AiLCJvbmVuZCIsIm9ubG9hZGVycm9yIiwiY29uc29sZSIsIm9ucGxheWVycm9yIiwicGF1c2UiLCJ1bmxvYWQiLCJzZXRWb2x1bWUiLCJjbGFtcGVkVm9sdW1lIiwiTWF0aCIsIm1heCIsIm1pbiIsInNldExvb3AiLCJwb3NpdGlvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAudioPlayer.ts\n");

/***/ }),

/***/ "(ssr)/./src/store/audioStore.ts":
/*!*********************************!*\
  !*** ./src/store/audioStore.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioStore: () => (/* binding */ useAudioStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useAudioStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // 初始状态\n        currentSound: null,\n        playState: {\n            isPlaying: false,\n            isPaused: false,\n            isLoading: false,\n            currentTime: 0,\n            duration: 0,\n            volume: 0.7,\n            isLooping: false\n        },\n        // 播放器UI初始状态\n        playerUI: {\n            mode: \"standard\",\n            isVisible: false,\n            position: \"bottom\",\n            isMinimized: false,\n            showTimerPanel: false,\n            showMixingPanel: false\n        },\n        mixingChannels: [],\n        maxChannels: 2,\n        masterVolume: 0.8,\n        timer: {\n            duration: 0,\n            isActive: false,\n            remainingTime: 0,\n            fadeOutDuration: 10,\n            autoStop: true\n        },\n        favorites: [],\n        recentlyPlayed: [],\n        userVolume: 0.7,\n        // 播放控制方法\n        setCurrentSound: (sound)=>{\n            set({\n                currentSound: sound\n            });\n            if (sound) {\n                get().addToRecentlyPlayed(sound.id);\n                // 自动显示播放器\n                get().setPlayerVisible(true);\n            }\n        },\n        updatePlayState: (newState)=>{\n            set((state)=>({\n                    playState: {\n                        ...state.playState,\n                        ...newState\n                    }\n                }));\n        },\n        // 混音控制方法\n        addMixingChannel: (sound)=>{\n            const { mixingChannels, maxChannels } = get();\n            if (mixingChannels.length >= maxChannels) {\n                console.warn(`MVP版本最多支持${maxChannels}个音频同时播放`);\n                return false;\n            }\n            const newChannel = {\n                id: `channel_${Date.now()}`,\n                soundId: sound.id,\n                volume: 0.7,\n                isMuted: false,\n                isActive: true\n            };\n            set((state)=>({\n                    mixingChannels: [\n                        ...state.mixingChannels,\n                        newChannel\n                    ]\n                }));\n            return true;\n        },\n        removeMixingChannel: (channelId)=>{\n            set((state)=>({\n                    mixingChannels: state.mixingChannels.filter((channel)=>channel.id !== channelId)\n                }));\n        },\n        updateChannelVolume: (channelId, volume)=>{\n            set((state)=>({\n                    mixingChannels: state.mixingChannels.map((channel)=>channel.id === channelId ? {\n                            ...channel,\n                            volume\n                        } : channel)\n                }));\n        },\n        setMasterVolume: (volume)=>{\n            set({\n                masterVolume: volume\n            });\n        },\n        // 定时器方法\n        setTimer: (duration)=>{\n            set({\n                timer: {\n                    duration,\n                    isActive: true,\n                    remainingTime: duration * 60,\n                    fadeOutDuration: 10,\n                    autoStop: true\n                }\n            });\n        },\n        clearTimer: ()=>{\n            set((state)=>({\n                    timer: {\n                        ...state.timer,\n                        isActive: false,\n                        remainingTime: 0\n                    }\n                }));\n        },\n        updateTimerRemaining: (remaining)=>{\n            set((state)=>({\n                    timer: {\n                        ...state.timer,\n                        remainingTime: remaining\n                    }\n                }));\n        },\n        // 用户偏好方法\n        addToFavorites: (soundId)=>{\n            set((state)=>({\n                    favorites: state.favorites.includes(soundId) ? state.favorites : [\n                        ...state.favorites,\n                        soundId\n                    ]\n                }));\n        },\n        removeFromFavorites: (soundId)=>{\n            set((state)=>({\n                    favorites: state.favorites.filter((id)=>id !== soundId)\n                }));\n        },\n        addToRecentlyPlayed: (soundId)=>{\n            set((state)=>{\n                const filtered = state.recentlyPlayed.filter((id)=>id !== soundId);\n                return {\n                    recentlyPlayed: [\n                        soundId,\n                        ...filtered\n                    ].slice(0, 20) // 保留最近20个\n                };\n            });\n        },\n        setUserVolume: (volume)=>{\n            set({\n                userVolume: volume\n            });\n        },\n        // 播放器UI控制方法实现\n        setPlayerMode: (mode)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        mode\n                    }\n                }));\n        },\n        setPlayerVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        isVisible: visible\n                    }\n                }));\n        },\n        setPlayerPosition: (position)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        position\n                    }\n                }));\n        },\n        togglePlayerMinimized: ()=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        isMinimized: !state.playerUI.isMinimized\n                    }\n                }));\n        },\n        setTimerPanelVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        showTimerPanel: visible\n                    }\n                }));\n        },\n        setMixingPanelVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        showMixingPanel: visible\n                    }\n                }));\n        }\n    }), {\n    name: \"noisesleep-audio-store\",\n    partialize: (state)=>({\n            favorites: state.favorites,\n            recentlyPlayed: state.recentlyPlayed,\n            userVolume: state.userVolume,\n            masterVolume: state.masterVolume,\n            playerUI: state.playerUI\n        })\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/store/audioStore.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"95df7b7bb871\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm9pc2VzbGVlcC13ZWIvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2JlNTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5NWRmN2I3YmI4NzFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/[locale]/audio-test/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/[locale]/audio-test/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/audio-test/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/[locale]/layout.tsx":
/*!*************************************!*\
  !*** ./src/app/[locale]/layout.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LocaleLayout),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/[locale]/layout.tsx\",\"import\":\"Noto_Sans_SC\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-noto-sans-sc\",\"display\":\"swap\"}],\"variableName\":\"notoSansSC\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/[locale]/layout.tsx\\\",\\\"import\\\":\\\"Noto_Sans_SC\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-noto-sans-sc\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"notoSansSC\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n/* harmony import */ var _i18n_routing__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/routing */ \"(rsc)/./src/i18n/routing.ts\");\n/* harmony import */ var _components_AudioPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AudioPlayer */ \"(rsc)/./src/components/AudioPlayer/index.ts\");\n\n\n\n\n\n\n\n\nfunction generateStaticParams() {\n    return _i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.map((locale)=>({\n            locale\n        }));\n}\nasync function generateMetadata({ params: { locale } }) {\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const meta = messages.meta;\n    return {\n        title: meta.title,\n        description: meta.description,\n        keywords: meta.keywords,\n        openGraph: {\n            title: meta.title,\n            description: meta.description,\n            url: `https://noisesleep.com${locale === \"en\" ? \"\" : \"/zh\"}`,\n            siteName: \"NoiseSleep\",\n            locale: locale === \"zh\" ? \"zh_CN\" : \"en_US\",\n            type: \"website\"\n        },\n        twitter: {\n            card: \"summary_large_image\",\n            title: meta.title,\n            description: meta.description\n        },\n        alternates: {\n            canonical: `https://noisesleep.com${locale === \"en\" ? \"\" : \"/zh\"}`,\n            languages: {\n                \"en\": \"https://noisesleep.com\",\n                \"zh\": \"https://noisesleep.com/zh\"\n            }\n        }\n    };\n}\nasync function LocaleLayout({ children, params: { locale } }) {\n    // 验证locale是否有效\n    if (!_i18n_routing__WEBPACK_IMPORTED_MODULE_2__.routing.locales.includes(locale)) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.notFound)();\n    }\n    // 获取翻译消息\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        dir: \"ltr\",\n        className: `${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_locale_layout_tsx_import_Noto_Sans_SC_arguments_subsets_latin_variable_font_noto_sans_sc_display_swap_variableName_notoSansSC___WEBPACK_IMPORTED_MODULE_6___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"en\",\n                        href: \"https://noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"zh\",\n                        href: \"https://noisesleep.com/zh\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"alternate\",\n                        hrefLang: \"x-default\",\n                        href: \"https://noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#f59e0b\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://cdn.noisesleep.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"Content-Security-Policy\",\n                        content: \" default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; media-src 'self' blob: https://cdn.noisesleep.com; connect-src 'self' https://www.google-analytics.com; \"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `\n          ${locale === \"zh\" ? \"font-noto-sans-sc\" : \"font-inter\"}\n          antialiased\n          bg-white dark:bg-gray-900\n          text-gray-900 dark:text-gray-100\n          transition-colors duration-300\n        `,\n                \"data-locale\": locale,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    messages: messages,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_3__.AudioPlayerProvider, {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/[locale]/layout.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/[locale]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/app/layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUdNQTtBQUZpQjtBQUlSLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0M7WUFBS0MsV0FBV0wsK0pBQWU7c0JBQzdCRTs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbD5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJib2R5IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/AudioPlayer.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayer.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AudioPlayer: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayer.tsx#AudioPlayer`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/AudioPlayerProvider.tsx":
/*!************************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayerProvider.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AudioPlayerProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx#AudioPlayerProvider`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/PlayButton.tsx":
/*!***************************************************!*\
  !*** ./src/components/AudioPlayer/PlayButton.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GhostPlayButton: () => (/* binding */ e3),
/* harmony export */   LargePlayButton: () => (/* binding */ e4),
/* harmony export */   PlayButton: () => (/* binding */ e0),
/* harmony export */   PrimaryPlayButton: () => (/* binding */ e1),
/* harmony export */   SecondaryPlayButton: () => (/* binding */ e2),
/* harmony export */   SmallPlayButton: () => (/* binding */ e5)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#PlayButton`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#PrimaryPlayButton`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#SecondaryPlayButton`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#GhostPlayButton`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#LargePlayButton`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx#SmallPlayButton`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/ProgressBar.tsx":
/*!****************************************************!*\
  !*** ./src/components/AudioPlayer/ProgressBar.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProgressBar: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/ProgressBar.tsx#ProgressBar`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/StandardPlayer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/AudioPlayer/StandardPlayer.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   StandardPlayer: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx#StandardPlayer`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/VolumeControl.tsx":
/*!******************************************************!*\
  !*** ./src/components/AudioPlayer/VolumeControl.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   VolumeControl: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/VolumeControl.tsx#VolumeControl`);


/***/ }),

/***/ "(rsc)/./src/components/AudioPlayer/index.ts":
/*!*********************************************!*\
  !*** ./src/components/AudioPlayer/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayer: () => (/* reexport safe */ _AudioPlayer__WEBPACK_IMPORTED_MODULE_0__.AudioPlayer),\n/* harmony export */   AudioPlayerProvider: () => (/* reexport safe */ _AudioPlayerProvider__WEBPACK_IMPORTED_MODULE_5__.AudioPlayerProvider),\n/* harmony export */   GhostPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.GhostPlayButton),\n/* harmony export */   LargePlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.LargePlayButton),\n/* harmony export */   PlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.PlayButton),\n/* harmony export */   PrimaryPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.PrimaryPlayButton),\n/* harmony export */   ProgressBar: () => (/* reexport safe */ _ProgressBar__WEBPACK_IMPORTED_MODULE_3__.ProgressBar),\n/* harmony export */   SecondaryPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.SecondaryPlayButton),\n/* harmony export */   SmallPlayButton: () => (/* reexport safe */ _PlayButton__WEBPACK_IMPORTED_MODULE_1__.SmallPlayButton),\n/* harmony export */   StandardPlayer: () => (/* reexport safe */ _StandardPlayer__WEBPACK_IMPORTED_MODULE_4__.StandardPlayer),\n/* harmony export */   VolumeControl: () => (/* reexport safe */ _VolumeControl__WEBPACK_IMPORTED_MODULE_2__.VolumeControl)\n/* harmony export */ });\n/* harmony import */ var _AudioPlayer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AudioPlayer */ \"(rsc)/./src/components/AudioPlayer/AudioPlayer.tsx\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PlayButton */ \"(rsc)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./VolumeControl */ \"(rsc)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ProgressBar */ \"(rsc)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* harmony import */ var _StandardPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StandardPlayer */ \"(rsc)/./src/components/AudioPlayer/StandardPlayer.tsx\");\n/* harmony import */ var _AudioPlayerProvider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AudioPlayerProvider */ \"(rsc)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\");\n// 主要组件\n\n\n\n\n\n // 类型定义\n // Props types are not exported from components\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9BdWRpb1BsYXllci9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLE9BQU87QUFDcUM7QUFDeUY7QUFDckY7QUFDSjtBQUNNO0FBQ1UsQ0FFNUQsT0FBTztDQUNQLCtDQUErQyIsInNvdXJjZXMiOlsid2VicGFjazovL25vaXNlc2xlZXAtd2ViLy4vc3JjL2NvbXBvbmVudHMvQXVkaW9QbGF5ZXIvaW5kZXgudHM/NmFjOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyDkuLvopoHnu4Tku7ZcbmV4cG9ydCB7IEF1ZGlvUGxheWVyIH0gZnJvbSAnLi9BdWRpb1BsYXllcic7XG5leHBvcnQgeyBQbGF5QnV0dG9uLCBQcmltYXJ5UGxheUJ1dHRvbiwgU2Vjb25kYXJ5UGxheUJ1dHRvbiwgR2hvc3RQbGF5QnV0dG9uLCBMYXJnZVBsYXlCdXR0b24sIFNtYWxsUGxheUJ1dHRvbiB9IGZyb20gJy4vUGxheUJ1dHRvbic7XG5leHBvcnQgeyBWb2x1bWVDb250cm9sIH0gZnJvbSAnLi9Wb2x1bWVDb250cm9sJztcbmV4cG9ydCB7IFByb2dyZXNzQmFyIH0gZnJvbSAnLi9Qcm9ncmVzc0Jhcic7XG5leHBvcnQgeyBTdGFuZGFyZFBsYXllciB9IGZyb20gJy4vU3RhbmRhcmRQbGF5ZXInO1xuZXhwb3J0IHsgQXVkaW9QbGF5ZXJQcm92aWRlciB9IGZyb20gJy4vQXVkaW9QbGF5ZXJQcm92aWRlcic7XG5cbi8vIOexu+Wei+WumuS5iVxuLy8gUHJvcHMgdHlwZXMgYXJlIG5vdCBleHBvcnRlZCBmcm9tIGNvbXBvbmVudHNcbiJdLCJuYW1lcyI6WyJBdWRpb1BsYXllciIsIlBsYXlCdXR0b24iLCJQcmltYXJ5UGxheUJ1dHRvbiIsIlNlY29uZGFyeVBsYXlCdXR0b24iLCJHaG9zdFBsYXlCdXR0b24iLCJMYXJnZVBsYXlCdXR0b24iLCJTbWFsbFBsYXlCdXR0b24iLCJWb2x1bWVDb250cm9sIiwiUHJvZ3Jlc3NCYXIiLCJTdGFuZGFyZFBsYXllciIsIkF1ZGlvUGxheWVyUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/AudioPlayer/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n/request.ts":
/*!*****************************!*\
  !*** ./src/i18n/request.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./routing */ \"(rsc)/./src/i18n/routing.ts\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ({ locale })=>{\n    // 验证传入的locale是否有效，如果无效则使用默认locale\n    if (!_routing__WEBPACK_IMPORTED_MODULE_0__.routing.locales.includes(locale)) {\n        console.warn(`Invalid locale: ${locale}, using default locale: ${_routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale}`);\n        locale = _routing__WEBPACK_IMPORTED_MODULE_0__.routing.defaultLocale;\n    }\n    return {\n        messages: (await __webpack_require__(\"(rsc)/./src/i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default,\n        timeZone: locale === \"zh\" ? \"Asia/Shanghai\" : \"America/New_York\",\n        now: new Date(),\n        formats: {\n            dateTime: {\n                short: {\n                    day: \"numeric\",\n                    month: \"short\",\n                    year: \"numeric\"\n                }\n            },\n            number: {\n                precise: {\n                    maximumFractionDigits: 2\n                }\n            }\n        }\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./src/i18n/routing.ts":
/*!*****************************!*\
  !*** ./src/i18n/routing.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   routing: () => (/* binding */ routing)\n/* harmony export */ });\n/* harmony import */ var next_intl_routing__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-intl/routing */ \"(rsc)/./node_modules/next-intl/dist/development/routing.js\");\n\nconst routing = (0,next_intl_routing__WEBPACK_IMPORTED_MODULE_0__.defineRouting)({\n    // 支持的语言列表\n    locales: [\n        \"en\",\n        \"zh\"\n    ],\n    // 默认语言\n    defaultLocale: \"en\",\n    // 语言前缀配置\n    localePrefix: {\n        mode: \"as-needed\",\n        prefixes: {\n            \"zh\": \"/zh\"\n        }\n    },\n    // 路径名配置\n    pathnames: {\n        \"/\": \"/\",\n        \"/about\": \"/about\",\n        \"/sounds\": \"/sounds\",\n        \"/sounds/[category]\": {\n            en: \"/sounds/[category]\",\n            zh: \"/sounds/[category]\"\n        },\n        \"/sounds/[category]/[sound]\": {\n            en: \"/sounds/[category]/[sound]\",\n            zh: \"/sounds/[category]/[sound]\"\n        },\n        \"/mix\": \"/mix\",\n        \"/favorites\": \"/favorites\",\n        \"/settings\": \"/settings\"\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/i18n/routing.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/@formatjs","vendor-chunks/howler","vendor-chunks/use-intl","vendor-chunks/intl-messageformat","vendor-chunks/tslib","vendor-chunks/zustand","vendor-chunks/next-intl","vendor-chunks/use-sync-external-store","vendor-chunks/@heroicons","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F%5Blocale%5D%2Faudio-test%2Fpage&page=%2F%5Blocale%5D%2Faudio-test%2Fpage&appPaths=%2F%5Blocale%5D%2Faudio-test%2Fpage&pagePath=private-next-app-dir%2F%5Blocale%5D%2Faudio-test%2Fpage.tsx&appDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fo_o%2FDocuments%2FNoiseSleep&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();