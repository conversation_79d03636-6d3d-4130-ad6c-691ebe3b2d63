"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/audio-test/page",{

/***/ "(app-pages-browser)/./src/store/audioStore.ts":
/*!*********************************!*\
  !*** ./src/store/audioStore.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioStore: function() { return /* binding */ useAudioStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useAudioStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // 初始状态\n        currentSound: null,\n        playState: {\n            isPlaying: false,\n            isPaused: false,\n            isLoading: false,\n            currentTime: 0,\n            duration: 0,\n            volume: 0.7,\n            isLooping: false\n        },\n        // 播放器UI初始状态\n        playerUI: {\n            mode: \"standard\",\n            isVisible: false,\n            position: \"bottom\",\n            isMinimized: false,\n            showTimerPanel: false,\n            showMixingPanel: false\n        },\n        mixingChannels: [],\n        maxChannels: 2,\n        masterVolume: 0.8,\n        timer: {\n            duration: 0,\n            isActive: false,\n            remainingTime: 0,\n            fadeOutDuration: 10,\n            autoStop: true\n        },\n        favorites: [],\n        recentlyPlayed: [],\n        userVolume: 0.7,\n        // 播放控制方法\n        setCurrentSound: (sound)=>{\n            console.log(\"\\uD83C\\uDFB5 设置当前音频:\", {\n                sound: sound === null || sound === void 0 ? void 0 : sound.title,\n                isVisible: get().playerUI.isVisible\n            });\n            set({\n                currentSound: sound\n            });\n            if (sound) {\n                get().addToRecentlyPlayed(sound.id);\n                // 自动显示播放器\n                console.log(\"\\uD83D\\uDC41️ 显示播放器\");\n                get().setPlayerVisible(true);\n            }\n        },\n        updatePlayState: (newState)=>{\n            set((state)=>({\n                    playState: {\n                        ...state.playState,\n                        ...newState\n                    }\n                }));\n        },\n        // 混音控制方法\n        addMixingChannel: (sound)=>{\n            const { mixingChannels, maxChannels } = get();\n            if (mixingChannels.length >= maxChannels) {\n                console.warn(\"MVP版本最多支持\".concat(maxChannels, \"个音频同时播放\"));\n                return false;\n            }\n            const newChannel = {\n                id: \"channel_\".concat(Date.now()),\n                soundId: sound.id,\n                volume: 0.7,\n                isMuted: false,\n                isActive: true\n            };\n            set((state)=>({\n                    mixingChannels: [\n                        ...state.mixingChannels,\n                        newChannel\n                    ]\n                }));\n            return true;\n        },\n        removeMixingChannel: (channelId)=>{\n            set((state)=>({\n                    mixingChannels: state.mixingChannels.filter((channel)=>channel.id !== channelId)\n                }));\n        },\n        updateChannelVolume: (channelId, volume)=>{\n            set((state)=>({\n                    mixingChannels: state.mixingChannels.map((channel)=>channel.id === channelId ? {\n                            ...channel,\n                            volume\n                        } : channel)\n                }));\n        },\n        setMasterVolume: (volume)=>{\n            set({\n                masterVolume: volume\n            });\n        },\n        // 定时器方法\n        setTimer: (duration)=>{\n            set({\n                timer: {\n                    duration,\n                    isActive: true,\n                    remainingTime: duration * 60,\n                    fadeOutDuration: 10,\n                    autoStop: true\n                }\n            });\n        },\n        clearTimer: ()=>{\n            set((state)=>({\n                    timer: {\n                        ...state.timer,\n                        isActive: false,\n                        remainingTime: 0\n                    }\n                }));\n        },\n        updateTimerRemaining: (remaining)=>{\n            set((state)=>({\n                    timer: {\n                        ...state.timer,\n                        remainingTime: remaining\n                    }\n                }));\n        },\n        // 用户偏好方法\n        addToFavorites: (soundId)=>{\n            set((state)=>({\n                    favorites: state.favorites.includes(soundId) ? state.favorites : [\n                        ...state.favorites,\n                        soundId\n                    ]\n                }));\n        },\n        removeFromFavorites: (soundId)=>{\n            set((state)=>({\n                    favorites: state.favorites.filter((id)=>id !== soundId)\n                }));\n        },\n        addToRecentlyPlayed: (soundId)=>{\n            set((state)=>{\n                const filtered = state.recentlyPlayed.filter((id)=>id !== soundId);\n                return {\n                    recentlyPlayed: [\n                        soundId,\n                        ...filtered\n                    ].slice(0, 20) // 保留最近20个\n                };\n            });\n        },\n        setUserVolume: (volume)=>{\n            set({\n                userVolume: volume\n            });\n        },\n        // 播放器UI控制方法实现\n        setPlayerMode: (mode)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        mode\n                    }\n                }));\n        },\n        setPlayerVisible: (visible)=>{\n            console.log(\"\\uD83D\\uDC41️ 设置播放器可见性:\", visible);\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        isVisible: visible\n                    }\n                }));\n        },\n        setPlayerPosition: (position)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        position\n                    }\n                }));\n        },\n        togglePlayerMinimized: ()=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        isMinimized: !state.playerUI.isMinimized\n                    }\n                }));\n        },\n        setTimerPanelVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        showTimerPanel: visible\n                    }\n                }));\n        },\n        setMixingPanelVisible: (visible)=>{\n            set((state)=>({\n                    playerUI: {\n                        ...state.playerUI,\n                        showMixingPanel: visible\n                    }\n                }));\n        }\n    }), {\n    name: \"noisesleep-audio-store\",\n    partialize: (state)=>({\n            favorites: state.favorites,\n            recentlyPlayed: state.recentlyPlayed,\n            userVolume: state.userVolume,\n            masterVolume: state.masterVolume,\n            playerUI: state.playerUI\n        })\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/audioStore.ts\n"));

/***/ })

});