"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/components/AudioPlayer/StandardPlayer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/AudioPlayer/StandardPlayer.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StandardPlayer: function() { return /* binding */ StandardPlayer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AnimatePresence,motion!=!framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/audioStore */ \"(app-pages-browser)/./src/store/audioStore.ts\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(app-pages-browser)/./src/hooks/useAudioPlayer.ts\");\n/* harmony import */ var _PlayButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./PlayButton */ \"(app-pages-browser)/./src/components/AudioPlayer/PlayButton.tsx\");\n/* harmony import */ var _VolumeControl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./VolumeControl */ \"(app-pages-browser)/./src/components/AudioPlayer/VolumeControl.tsx\");\n/* harmony import */ var _ProgressBar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ProgressBar */ \"(app-pages-browser)/./src/components/AudioPlayer/ProgressBar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StopIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SpeakerWaveIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MoonIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ClockIcon,MinusIcon,MoonIcon,SpeakerWaveIcon,StopIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ StandardPlayer auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction StandardPlayer(param) {\n    let { className, position = \"bottom\", showMixingButton = true, showSleepModeButton = true, autoHide = false, autoHideDelay = 5000 } = param;\n    var _currentSound_description, _currentSound_description1;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"audioPlayer\");\n    const tCommon = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"common\");\n    const { currentSound, playerUI, favorites, setPlayerVisible, setPlayerMode, togglePlayerMinimized, setTimerPanelVisible, setMixingPanelVisible, addToFavorites, removeFromFavorites } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore)();\n    const { play, pause, stop, setVolume, seek, isPlaying, isPaused, isLoading, currentTime, duration, volume, error } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__.useAudioPlayer)();\n    // 当有音频播放时自动显示播放器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (currentSound && !playerUI.isVisible) {\n            setPlayerVisible(true);\n        }\n    }, [\n        currentSound,\n        playerUI.isVisible,\n        setPlayerVisible\n    ]);\n    // 自动隐藏功能\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoHide && !isPlaying && playerUI.isVisible) {\n            const timer = setTimeout(()=>{\n                setPlayerVisible(false);\n            }, autoHideDelay);\n            return ()=>clearTimeout(timer);\n        }\n        return undefined;\n    }, [\n        autoHide,\n        isPlaying,\n        playerUI.isVisible,\n        autoHideDelay,\n        setPlayerVisible\n    ]);\n    // 播放控制处理\n    const handlePlay = ()=>{\n        if (currentSound) {\n            play();\n        }\n    };\n    const handlePause = ()=>{\n        pause();\n    };\n    const handleStop = ()=>{\n        stop();\n        setPlayerVisible(false);\n    };\n    const handleVolumeChange = (newVolume)=>{\n        setVolume(newVolume);\n    };\n    const handleSeek = (position)=>{\n        seek(position);\n    };\n    // 收藏控制\n    const isFavorite = currentSound ? favorites.includes(currentSound.id) : false;\n    const handleToggleFavorite = ()=>{\n        if (currentSound) {\n            if (isFavorite) {\n                removeFromFavorites(currentSound.id);\n            } else {\n                addToFavorites(currentSound.id);\n            }\n        }\n    };\n    // 面板控制\n    const handleTimerClick = ()=>{\n        setTimerPanelVisible(!playerUI.showTimerPanel);\n    };\n    const handleMixingClick = ()=>{\n        setMixingPanelVisible(!playerUI.showMixingPanel);\n    };\n    const handleSleepModeClick = ()=>{\n        setPlayerMode(\"sleep\");\n    };\n    const handleMinimize = ()=>{\n        togglePlayerMinimized();\n    };\n    const handleClose = ()=>{\n        stop();\n        setPlayerVisible(false);\n    };\n    // 位置样式 - 响应式优化\n    const positionClasses = {\n        bottom: \"fixed bottom-0 left-0 right-0 z-50\",\n        top: \"fixed top-0 left-0 right-0 z-50\",\n        floating: \"fixed bottom-4 left-2 right-2 sm:left-4 sm:right-4 z-50 max-w-4xl mx-auto\"\n    };\n    // 动画变体\n    const playerVariants = {\n        hidden: {\n            y: position === \"bottom\" ? 100 : position === \"top\" ? -100 : 20,\n            opacity: 0\n        },\n        visible: {\n            y: 0,\n            opacity: 1,\n            transition: {\n                type: \"spring\",\n                stiffness: 300,\n                damping: 30\n            }\n        },\n        exit: {\n            y: position === \"bottom\" ? 100 : position === \"top\" ? -100 : 20,\n            opacity: 0,\n            transition: {\n                duration: 0.2\n            }\n        }\n    };\n    const minimizedVariants = {\n        normal: {\n            height: \"auto\"\n        },\n        minimized: {\n            height: 60,\n            transition: {\n                duration: 0.3\n            }\n        }\n    };\n    // 调试信息\n    console.log(\"\\uD83C\\uDFB5 StandardPlayer 渲染检查:\", {\n        isVisible: playerUI.isVisible,\n        currentSound: currentSound === null || currentSound === void 0 ? void 0 : currentSound.title,\n        shouldRender: playerUI.isVisible && currentSound\n    });\n    if (!playerUI.isVisible || !currentSound) {\n        console.log(\"❌ StandardPlayer 不渲染 - isVisible:\", playerUI.isVisible, \"currentSound:\", !!currentSound);\n        return null;\n    }\n    console.log(\"✅ StandardPlayer 正在渲染\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n            initial: \"hidden\",\n            animate: \"visible\",\n            exit: \"exit\",\n            variants: playerVariants,\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(positionClasses[position], \"bg-white/95 dark:bg-gray-900/95 backdrop-blur-md\", \"border-t border-gray-200 dark:border-gray-700\", position === \"floating\" && \"rounded-lg border shadow-xl\", className),\n            \"data-testid\": \"standard-player\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AnimatePresence_motion_framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                animate: playerUI.isMinimized ? \"minimized\" : \"normal\",\n                variants: minimizedVariants,\n                className: \"overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-3 sm:px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 sm:gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1 sm:gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PlayButton__WEBPACK_IMPORTED_MODULE_5__.PlayButton, {\n                                                        isPlaying: isPlaying,\n                                                        isLoading: isLoading,\n                                                        onPlay: handlePlay,\n                                                        onPause: handlePause,\n                                                        size: \"md\",\n                                                        variant: \"primary\",\n                                                        disabled: !currentSound\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleStop,\n                                                        disabled: !currentSound,\n                                                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"text-gray-600 dark:text-gray-400\"),\n                                                        \"aria-label\": t(\"controls.stop\"),\n                                                        title: t(\"controls.stop\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                        children: currentSound.title.zh || currentSound.title.en\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                        children: ((_currentSound_description = currentSound.description) === null || _currentSound_description === void 0 ? void 0 : _currentSound_description.zh) || ((_currentSound_description1 = currentSound.description) === null || _currentSound_description1 === void 0 ? void 0 : _currentSound_description1.en)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this),\n                                    !playerUI.isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ProgressBar__WEBPACK_IMPORTED_MODULE_7__.ProgressBar, {\n                                            currentTime: currentTime,\n                                            duration: duration,\n                                            onSeek: handleSeek,\n                                            isLoading: isLoading,\n                                            showTime: true,\n                                            size: \"md\",\n                                            disabled: !currentSound\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1 sm:gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VolumeControl__WEBPACK_IMPORTED_MODULE_6__.VolumeControl, {\n                                            volume: volume,\n                                            onVolumeChange: handleVolumeChange,\n                                            size: \"sm\",\n                                            showIcon: true,\n                                            disabled: !currentSound\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    !playerUI.isMinimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleTimerClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"hidden sm:block p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", playerUI.showTimerPanel ? \"text-amber-600 bg-amber-50 dark:bg-amber-900/20\" : \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"timer.setTimer\"),\n                                                title: t(\"timer.setTimer\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, this),\n                                            showMixingButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleMixingClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"hidden sm:block p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", playerUI.showMixingPanel ? \"text-amber-600 bg-amber-50 dark:bg-amber-900/20\" : \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"mixing.title\"),\n                                                title: t(\"mixing.title\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 23\n                                            }, this),\n                                            showSleepModeButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleSleepModeClick,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-2 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-600 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"modes.switchToSleep\"),\n                                                title: t(\"modes.switchToSleep\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1 ml-2 border-l border-gray-200 dark:border-gray-700 pl-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleMinimize,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1.5 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-500 dark:text-gray-400\"),\n                                                \"aria-label\": playerUI.isMinimized ? t(\"controls.maximize\") : t(\"controls.minimize\"),\n                                                title: playerUI.isMinimized ? t(\"controls.maximize\") : t(\"controls.minimize\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleClose,\n                                                className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1.5 rounded-md transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-800\", \"focus:outline-none focus:ring-2 focus:ring-amber-500\", \"text-gray-500 dark:text-gray-400\"),\n                                                \"aria-label\": t(\"controls.close\"),\n                                                title: t(\"controls.close\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ClockIcon_MinusIcon_MoonIcon_SpeakerWaveIcon_StopIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/StandardPlayer.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(StandardPlayer, \"jHX5YDR/z/FJAn8HNk4rwVCaGrg=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        _store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore,\n        _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__.useAudioPlayer\n    ];\n});\n_c = StandardPlayer;\nvar _c;\n$RefreshReg$(_c, \"StandardPlayer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioPlayer/StandardPlayer.tsx\n"));

/***/ })

});