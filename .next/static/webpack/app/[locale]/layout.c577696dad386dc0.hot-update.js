"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/layout",{

/***/ "(app-pages-browser)/./src/components/AudioPlayer/AudioPlayerProvider.tsx":
/*!************************************************************!*\
  !*** ./src/components/AudioPlayer/AudioPlayerProvider.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioPlayerProvider: function() { return /* binding */ AudioPlayerProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/audioStore */ \"(app-pages-browser)/./src/store/audioStore.ts\");\n/* harmony import */ var _StandardPlayer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StandardPlayer */ \"(app-pages-browser)/./src/components/AudioPlayer/StandardPlayer.tsx\");\n/* __next_internal_client_entry_do_not_use__ AudioPlayerProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AudioPlayerProvider(param) {\n    let { children, className } = param;\n    _s();\n    const { currentSound, playerUI, setPlayerVisible } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore)();\n    // 监听音频播放状态，自动显示/隐藏播放器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D AudioPlayerProvider useEffect:\", {\n            currentSound: currentSound === null || currentSound === void 0 ? void 0 : currentSound.title,\n            isVisible: playerUI.isVisible,\n            mode: playerUI.mode\n        });\n        if (currentSound && !playerUI.isVisible) {\n            console.log(\"\\uD83D\\uDE80 显示播放器\");\n            setPlayerVisible(true);\n        }\n    }, [\n        currentSound,\n        playerUI.isVisible,\n        setPlayerVisible\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            children,\n            playerUI.mode === \"standard\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StandardPlayer__WEBPACK_IMPORTED_MODULE_3__.StandardPlayer, {\n                position: playerUI.position,\n                showMixingButton: true,\n                showSleepModeButton: true,\n                autoHide: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this),\n            playerUI.mode === \"sleep\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-gray-900 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold mb-4\",\n                            children: \"睡眠模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300 mb-8\",\n                            children: \"睡眠模式界面将在第三阶段实现\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore.getState().setPlayerMode(\"standard\"),\n                            className: \"px-6 py-3 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors\",\n                            children: \"返回标准模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/AudioPlayerProvider.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioPlayerProvider, \"gJFiaeFbX4dfuuwhWnBS5uONhQM=\", false, function() {\n    return [\n        _store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore\n    ];\n});\n_c = AudioPlayerProvider;\nvar _c;\n$RefreshReg$(_c, \"AudioPlayerProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioPlayer/AudioPlayerProvider.tsx\n"));

/***/ })

});