"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/sounds/[category]/page",{

/***/ "(app-pages-browser)/./src/components/AudioPlayer/PlayButton.tsx":
/*!***************************************************!*\
  !*** ./src/components/AudioPlayer/PlayButton.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GhostPlayButton: function() { return /* binding */ GhostPlayButton; },\n/* harmony export */   LargePlayButton: function() { return /* binding */ LargePlayButton; },\n/* harmony export */   PlayButton: function() { return /* binding */ PlayButton; },\n/* harmony export */   PrimaryPlayButton: function() { return /* binding */ PrimaryPlayButton; },\n/* harmony export */   SecondaryPlayButton: function() { return /* binding */ SecondaryPlayButton; },\n/* harmony export */   SmallPlayButton: function() { return /* binding */ SmallPlayButton; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ PlayButton,PrimaryPlayButton,SecondaryPlayButton,GhostPlayButton,LargePlayButton,SmallPlayButton auto */ \nvar _s = $RefreshSig$();\n\n\nfunction PlayButton(param) {\n    let { isPlaying, isLoading, onPlay, onPause, size = \"md\", variant = \"primary\", disabled = false, className } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations)(\"common\");\n    const handleClick = ()=>{\n        console.log(\"\\uD83C\\uDFB5 PlayButton handleClick 被调用:\", {\n            isPlaying,\n            disabled,\n            isLoading\n        });\n        if (disabled || isLoading) return;\n        if (isPlaying) {\n            console.log(\"⏸️ 调用 onPause\");\n            onPause();\n        } else {\n            console.log(\"▶️ 调用 onPlay\");\n            onPlay();\n        }\n    };\n    const sizeClasses = {\n        sm: \"w-8 h-8 p-1.5\",\n        md: \"w-12 h-12 p-3\",\n        lg: \"w-16 h-16 p-4\"\n    };\n    const variantClasses = {\n        primary: \"bg-amber-500 hover:bg-amber-600 text-white shadow-lg hover:shadow-xl\",\n        secondary: \"bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200\",\n        ghost: \"bg-transparent hover:bg-gray-100 text-gray-600 dark:hover:bg-gray-800 dark:text-gray-400\"\n    };\n    const iconSize = {\n        sm: \"w-3 h-3\",\n        md: \"w-4 h-4\",\n        lg: \"w-6 h-6\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleClick,\n        disabled: disabled || isLoading,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(// 基础样式\n        \"relative rounded-full transition-all duration-200 ease-in-out\", \"focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2\", \"disabled:opacity-50 disabled:cursor-not-allowed\", \"transform hover:scale-105 active:scale-95\", // 尺寸样式\n        sizeClasses[size], // 变体样式\n        variantClasses[variant], // 自定义类名\n        className),\n        \"aria-label\": isPlaying ? t(\"pause\") : t(\"play\"),\n        title: isPlaying ? t(\"pause\") : t(\"play\"),\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(\"animate-spin rounded-full border-2 border-current border-t-transparent\", iconSize[size])\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, this),\n            !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: isPlaying ? // 暂停图标\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: iconSize[size],\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M6 4h4v16H6V4zm8 0h4v16h-4V4z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 13\n                }, this) : // 播放图标\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__.clsx)(iconSize[size], \"ml-0.5\"),\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M8 5v14l11-7z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 96,\n                columnNumber: 9\n            }, this),\n            isPlaying && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-full animate-ping bg-current opacity-20\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_s(PlayButton, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_2__.useTranslations\n    ];\n});\n_c = PlayButton;\n// 预设的播放按钮变体\nfunction PrimaryPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"primary\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 131,\n        columnNumber: 10\n    }, this);\n}\n_c1 = PrimaryPlayButton;\nfunction SecondaryPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"secondary\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 135,\n        columnNumber: 10\n    }, this);\n}\n_c2 = SecondaryPlayButton;\nfunction GhostPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        variant: \"ghost\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 139,\n        columnNumber: 10\n    }, this);\n}\n_c3 = GhostPlayButton;\n// 大型播放按钮（用于主播放器）\nfunction LargePlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        size: \"lg\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 144,\n        columnNumber: 10\n    }, this);\n}\n_c4 = LargePlayButton;\n// 小型播放按钮（用于列表项）\nfunction SmallPlayButton(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PlayButton, {\n        ...props,\n        size: \"sm\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioPlayer/PlayButton.tsx\",\n        lineNumber: 149,\n        columnNumber: 10\n    }, this);\n}\n_c5 = SmallPlayButton;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"PlayButton\");\n$RefreshReg$(_c1, \"PrimaryPlayButton\");\n$RefreshReg$(_c2, \"SecondaryPlayButton\");\n$RefreshReg$(_c3, \"GhostPlayButton\");\n$RefreshReg$(_c4, \"LargePlayButton\");\n$RefreshReg$(_c5, \"SmallPlayButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioPlayer/PlayButton.tsx\n"));

/***/ })

});