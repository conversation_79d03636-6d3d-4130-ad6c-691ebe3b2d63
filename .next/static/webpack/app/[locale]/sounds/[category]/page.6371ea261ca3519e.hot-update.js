"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/sounds/[category]/page",{

/***/ "(app-pages-browser)/./src/components/AudioCard/AudioCard.tsx":
/*!************************************************!*\
  !*** ./src/components/AudioCard/AudioCard.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioCard: function() { return /* binding */ AudioCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/audioStore */ \"(app-pages-browser)/./src/store/audioStore.ts\");\n/* harmony import */ var _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAudioPlayer */ \"(app-pages-browser)/./src/hooks/useAudioPlayer.ts\");\n/* harmony import */ var _components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AudioPlayer */ \"(app-pages-browser)/./src/components/AudioPlayer/index.ts\");\n/* __next_internal_client_entry_do_not_use__ AudioCard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AudioCard(param) {\n    let { audio, variant = \"default\", showCategory = true, showTags = true, showDuration = false, showDescription = false, onPlay, className } = param;\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"common\");\n    const locale = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale)();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { currentSound, playState, favorites, addToFavorites, removeFromFavorites } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore)();\n    const { play, pause, isPlaying, isLoading } = (0,_hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__.useAudioPlayer)();\n    const isCurrentlyPlaying = (currentSound === null || currentSound === void 0 ? void 0 : currentSound.id) === audio.id && isPlaying;\n    const isFavorite = favorites.includes(audio.id);\n    // 获取本地化文本\n    const getLocalizedText = (textObj)=>{\n        return textObj[locale] || textObj.en || Object.values(textObj)[0] || \"\";\n    };\n    const title = getLocalizedText(audio.title);\n    const description = audio.description ? getLocalizedText(audio.description) : \"\";\n    // 处理播放\n    const handlePlay = ()=>{\n        console.log(\"\\uD83C\\uDFB5 AudioCard handlePlay 被调用:\", {\n            audioTitle: title,\n            audioId: audio.id,\n            isCurrentlyPlaying,\n            currentSoundId: currentSound === null || currentSound === void 0 ? void 0 : currentSound.id\n        });\n        if (isCurrentlyPlaying) {\n            console.log(\"⏸️ 暂停音频\");\n            pause();\n        } else {\n            console.log(\"▶️ 播放音频\");\n            play(audio);\n        }\n        onPlay === null || onPlay === void 0 ? void 0 : onPlay(audio);\n    };\n    // 处理收藏\n    const handleToggleFavorite = (e)=>{\n        e.stopPropagation();\n        if (isFavorite) {\n            removeFromFavorites(audio.id);\n        } else {\n            addToFavorites(audio.id);\n        }\n    };\n    // 获取音频图标\n    const getAudioIcon = ()=>{\n        const iconMap = {\n            rain: \"\\uD83C\\uDF27️\",\n            nature: \"\\uD83C\\uDF3F\",\n            noise: \"\\uD83D\\uDD0A\",\n            animals: \"\\uD83D\\uDC3E\",\n            things: \"\\uD83C\\uDFE0\",\n            transport: \"\\uD83D\\uDE97\",\n            urban: \"\\uD83C\\uDFD9️\",\n            places: \"\\uD83D\\uDCCD\"\n        };\n        return iconMap[audio.category.toLowerCase()] || \"\\uD83C\\uDFB5\";\n    };\n    // 格式化时长\n    const formatDuration = (seconds)=>{\n        if (!seconds || seconds <= 0) return \"\";\n        const mins = Math.floor(seconds / 60);\n        const secs = Math.floor(seconds % 60);\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, \"0\"));\n    };\n    // 紧凑版本\n    if (variant === \"compact\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg\", \"border border-gray-200 dark:border-gray-700 hover:border-amber-300 dark:hover:border-amber-600\", \"transition-all duration-200 cursor-pointer group\", isCurrentlyPlaying && \"ring-2 ring-amber-500 border-amber-500\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__.SmallPlayButton, {\n                    isPlaying: isCurrentlyPlaying,\n                    isLoading: isLoading && (currentSound === null || currentSound === void 0 ? void 0 : currentSound.id) === audio.id,\n                    onPlay: handlePlay,\n                    onPause: handlePlay\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        showCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                            children: [\n                                getAudioIcon(),\n                                \" \",\n                                audio.category\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleToggleFavorite,\n                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1 rounded-full transition-colors opacity-0 group-hover:opacity-100\", isFavorite && \"opacity-100\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                    \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4\",\n                        fill: isFavorite ? \"currentColor\" : \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    // 详细版本\n    if (variant === \"detailed\") {\n        var _audio_tags;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700\", \"hover:shadow-md hover:border-amber-300 dark:hover:border-amber-600\", \"transition-all duration-200 overflow-hidden group\", isCurrentlyPlaying && \"ring-2 ring-amber-500 border-amber-500\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative h-32 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-4xl\",\n                                children: getAudioIcon()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__.SmallPlayButton, {\n                                isPlaying: isCurrentlyPlaying,\n                                isLoading: isLoading && (currentSound === null || currentSound === void 0 ? void 0 : currentSound.id) === audio.id,\n                                onPlay: handlePlay,\n                                onPause: handlePlay,\n                                variant: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleToggleFavorite,\n                            className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"absolute top-2 right-2 p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\", \"transition-all duration-200 opacity-0 group-hover:opacity-100\", isFavorite && \"opacity-100\", \"hover:bg-white dark:hover:bg-gray-800\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                            \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5\",\n                                fill: isFavorite ? \"currentColor\" : \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        showDescription && description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 mb-3\",\n                            children: [\n                                showCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 rounded-full\",\n                                    children: [\n                                        getAudioIcon(),\n                                        \" \",\n                                        audio.category\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this),\n                                showTags && ((_audio_tags = audio.tags) === null || _audio_tags === void 0 ? void 0 : _audio_tags.slice(0, 2).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-full\",\n                                        children: tag\n                                    }, tag, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\",\n                            children: [\n                                showDuration && audio.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: formatDuration(audio.duration)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this),\n                                audio.scientificRating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"⭐\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: audio.scientificRating.toFixed(1)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, this);\n    }\n    // 默认版本\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\", \"hover:shadow-md hover:border-amber-300 dark:hover:border-amber-600\", \"transition-all duration-200 overflow-hidden cursor-pointer group\", isCurrentlyPlaying && \"ring-2 ring-amber-500 border-amber-500\", className),\n        onClick: handlePlay,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-amber-100 to-amber-200 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: getAudioIcon()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this),\n                                        showCategory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: audio.category\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        isCurrentlyPlaying && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-3 bg-amber-500 rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-4 bg-amber-500 rounded-full animate-pulse\",\n                                    style: {\n                                        animationDelay: \"0.2s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-3 bg-amber-500 rounded-full animate-pulse\",\n                                    style: {\n                                        animationDelay: \"0.4s\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this),\n                showDescription && description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2\",\n                    children: description\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, this),\n                showTags && audio.tags && audio.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-1 mb-3\",\n                    children: [\n                        audio.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-full\",\n                                children: tag\n                            }, tag, false, {\n                                fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, this)),\n                        audio.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-500\",\n                            children: [\n                                \"+\",\n                                audio.tags.length - 3\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AudioPlayer__WEBPACK_IMPORTED_MODULE_5__.SmallPlayButton, {\n                                    isPlaying: isCurrentlyPlaying,\n                                    isLoading: isLoading && (currentSound === null || currentSound === void 0 ? void 0 : currentSound.id) === audio.id,\n                                    onPlay: ()=>{\n                                        handlePlay();\n                                    },\n                                    onPause: ()=>{\n                                        handlePlay();\n                                    },\n                                    variant: \"ghost\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                showDuration && audio.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                    children: formatDuration(audio.duration)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                audio.scientificRating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"⭐\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: audio.scientificRating.toFixed(1)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleToggleFavorite,\n                                    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(\"p-1 rounded-full transition-colors\", \"hover:bg-gray-100 dark:hover:bg-gray-700\", isFavorite ? \"text-red-500\" : \"text-gray-400\"),\n                                    \"aria-label\": isFavorite ? t(\"removeFromFavorites\") : t(\"addToFavorites\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: isFavorite ? \"currentColor\" : \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n            lineNumber: 262,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NoiseSleep/src/components/AudioCard/AudioCard.tsx\",\n        lineNumber: 253,\n        columnNumber: 5\n    }, this);\n}\n_s(AudioCard, \"6iBiiXNbvw2g2yS5cGee3VCPFxg=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useLocale,\n        _store_audioStore__WEBPACK_IMPORTED_MODULE_3__.useAudioStore,\n        _hooks_useAudioPlayer__WEBPACK_IMPORTED_MODULE_4__.useAudioPlayer\n    ];\n});\n_c = AudioCard;\nvar _c;\n$RefreshReg$(_c, \"AudioCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AudioCard/AudioCard.tsx\n"));

/***/ })

});