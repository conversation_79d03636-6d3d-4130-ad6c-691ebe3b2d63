"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/sounds/page",{

/***/ "(app-pages-browser)/./src/hooks/useAudioPlayer.ts":
/*!*************************************!*\
  !*** ./src/hooks/useAudioPlayer.ts ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAudioPlayer: function() { return /* binding */ useAudioPlayer; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! howler */ \"(app-pages-browser)/./node_modules/howler/dist/howler.js\");\n/* harmony import */ var howler__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(howler__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _store_audioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/audioStore */ \"(app-pages-browser)/./src/store/audioStore.ts\");\n\n\n\nconst useAudioPlayer = ()=>{\n    const howlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const { currentSound, playState, userVolume, setCurrentSound, updatePlayState, setUserVolume } = (0,_store_audioStore__WEBPACK_IMPORTED_MODULE_2__.useAudioStore)();\n    // 清理定时器\n    const clearProgressInterval = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (intervalRef.current) {\n            clearInterval(intervalRef.current);\n            intervalRef.current = null;\n        }\n    }, []);\n    // 开始进度更新定时器\n    const startProgressInterval = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        clearProgressInterval();\n        intervalRef.current = setInterval(()=>{\n            if (howlRef.current && howlRef.current.playing()) {\n                const currentTime = howlRef.current.seek();\n                updatePlayState({\n                    currentTime\n                });\n            }\n        }, 1000);\n    }, [\n        updatePlayState,\n        clearProgressInterval\n    ]);\n    // 播放音频\n    const play = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((sound)=>{\n        try {\n            console.log(\"\\uD83C\\uDFB5 播放音频被调用:\", {\n                sound: sound === null || sound === void 0 ? void 0 : sound.title,\n                currentSound: currentSound === null || currentSound === void 0 ? void 0 : currentSound.title\n            });\n            // 如果传入了新的音频，先停止当前播放\n            if (sound && sound.id !== (currentSound === null || currentSound === void 0 ? void 0 : currentSound.id)) {\n                console.log(\"\\uD83D\\uDD04 切换到新音频:\", sound.title);\n                stop();\n                setCurrentSound(sound);\n                // 创建新的 Howl 实例 - 映射小写分类到大写文件夹\n                const categoryMap = {\n                    \"rain\": \"Rain\",\n                    \"nature\": \"Nature\",\n                    \"noise\": \"Noise\",\n                    \"animals\": \"Animals\",\n                    \"things\": \"Things\",\n                    \"transport\": \"Transport\",\n                    \"urban\": \"Urban\",\n                    \"places\": \"Places\",\n                    \"ocean\": \"Ocean\"\n                };\n                const folderName = categoryMap[sound.category] || sound.category;\n                const audioUrl = \"/Sounds/\".concat(folderName, \"/\").concat(sound.filename);\n                howlRef.current = new howler__WEBPACK_IMPORTED_MODULE_1__.Howl({\n                    src: [\n                        audioUrl\n                    ],\n                    volume: userVolume,\n                    loop: playState.isLooping,\n                    onload: ()=>{\n                        var _howlRef_current;\n                        updatePlayState({\n                            isLoading: false,\n                            duration: ((_howlRef_current = howlRef.current) === null || _howlRef_current === void 0 ? void 0 : _howlRef_current.duration()) || 0,\n                            error: undefined\n                        });\n                    },\n                    onplay: ()=>{\n                        updatePlayState({\n                            isPlaying: true,\n                            isPaused: false,\n                            error: undefined\n                        });\n                        startProgressInterval();\n                    },\n                    onpause: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: true\n                        });\n                        clearProgressInterval();\n                    },\n                    onstop: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: false,\n                            currentTime: 0\n                        });\n                        clearProgressInterval();\n                    },\n                    onend: ()=>{\n                        updatePlayState({\n                            isPlaying: false,\n                            isPaused: false,\n                            currentTime: 0\n                        });\n                        clearProgressInterval();\n                    },\n                    onloaderror: (id, error)=>{\n                        console.error(\"音频加载失败:\", error);\n                        updatePlayState({\n                            isLoading: false,\n                            error: \"音频加载失败\"\n                        });\n                    },\n                    onplayerror: (id, error)=>{\n                        console.error(\"音频播放失败:\", error);\n                        updatePlayState({\n                            isPlaying: false,\n                            error: \"音频播放失败\"\n                        });\n                    }\n                });\n                updatePlayState({\n                    isLoading: true\n                });\n            }\n            // 播放音频\n            if (howlRef.current) {\n                if (playState.isPaused) {\n                    howlRef.current.play();\n                } else {\n                    howlRef.current.play();\n                }\n            }\n        } catch (error) {\n            console.error(\"播放音频时发生错误:\", error);\n            updatePlayState({\n                error: \"播放失败\",\n                isLoading: false\n            });\n        }\n    }, [\n        currentSound,\n        playState.isLooping,\n        playState.isPaused,\n        userVolume,\n        setCurrentSound,\n        updatePlayState,\n        startProgressInterval\n    ]);\n    // 暂停音频\n    const pause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (howlRef.current && howlRef.current.playing()) {\n            howlRef.current.pause();\n        }\n    }, []);\n    // 停止音频\n    const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (howlRef.current) {\n            howlRef.current.stop();\n            howlRef.current.unload();\n            howlRef.current = null;\n        }\n        clearProgressInterval();\n        updatePlayState({\n            isPlaying: false,\n            isPaused: false,\n            currentTime: 0\n        });\n    }, [\n        clearProgressInterval,\n        updatePlayState\n    ]);\n    // 设置音量\n    const setVolume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((volume)=>{\n        const clampedVolume = Math.max(0, Math.min(1, volume));\n        setUserVolume(clampedVolume);\n        updatePlayState({\n            volume: clampedVolume\n        });\n        if (howlRef.current) {\n            howlRef.current.volume(clampedVolume);\n        }\n        // 设置全局音量\n        howler__WEBPACK_IMPORTED_MODULE_1__.Howler.volume(clampedVolume);\n    }, [\n        setUserVolume,\n        updatePlayState\n    ]);\n    // 设置循环播放\n    const setLoop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((loop)=>{\n        updatePlayState({\n            isLooping: loop\n        });\n        if (howlRef.current) {\n            howlRef.current.loop(loop);\n        }\n    }, [\n        updatePlayState\n    ]);\n    // 跳转到指定位置\n    const seek = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((position)=>{\n        if (howlRef.current) {\n            howlRef.current.seek(position);\n            updatePlayState({\n                currentTime: position\n            });\n        }\n    }, [\n        updatePlayState\n    ]);\n    // 组件卸载时清理资源\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        return ()=>{\n            stop();\n        };\n    }, [\n        stop\n    ]);\n    // 监听音量变化\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (howlRef.current && playState.volume !== userVolume) {\n            howlRef.current.volume(userVolume);\n        }\n    }, [\n        userVolume,\n        playState.volume\n    ]);\n    return {\n        play,\n        pause,\n        stop,\n        setVolume,\n        setLoop,\n        seek,\n        isPlaying: playState.isPlaying,\n        isPaused: playState.isPaused,\n        isLoading: playState.isLoading,\n        currentTime: playState.currentTime,\n        duration: playState.duration,\n        volume: playState.volume,\n        isLooping: playState.isLooping,\n        error: playState.error || null\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAudioPlayer.ts\n"));

/***/ })

});