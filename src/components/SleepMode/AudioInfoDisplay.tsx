'use client';

import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { useAudioStore } from '@/store/audioStore';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';
import { TimerDisplay } from '../Timer/TimerDisplay';

interface AudioInfoDisplayProps {
  className?: string;
  showTimer?: boolean;
}

export function AudioInfoDisplay({ 
  className = '',
  showTimer = true 
}: AudioInfoDisplayProps) {
  const t = useTranslations('sleepMode');
  const { currentSound } = useAudioStore();
  const { 
    currentTime, 
    duration, 
    volume,
    isLooping 
  } = useAudioPlayer();

  if (!currentSound) {
    return (
      <div className={`audio-info-display ${className}`}>
        <div className="text-center text-gray-500 dark:text-gray-400">
          <div className="text-lg font-medium mb-2">{t('noAudioSelected')}</div>
          <div className="text-sm">{t('selectAudioToStart')}</div>
        </div>
      </div>
    );
  }

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 计算进度百分比
  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <motion.div 
      className={`audio-info-display ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-center space-y-6">
        {/* 音频标题 */}
        <div>
          <h2 className="text-2xl font-light text-gray-900 dark:text-white mb-2">
            {currentSound.title.zh || currentSound.title.en}
          </h2>
          {currentSound.description && (
            <p className="text-sm text-gray-500 dark:text-gray-400 max-w-md mx-auto">
              {currentSound.description.zh || currentSound.description.en}
            </p>
          )}
        </div>

        {/* 播放进度 */}
        <div className="space-y-3">
          {/* 进度条 */}
          <div className="w-full max-w-sm mx-auto">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <motion.div 
                className="bg-amber-400 dark:bg-amber-500 h-1 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
          
          {/* 时间显示 */}
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 max-w-sm mx-auto">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* 音频属性 */}
        <div className="grid grid-cols-2 gap-4 max-w-xs mx-auto text-sm">
          {/* 音量 */}
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400 mb-1">
              {t('volume')}
            </div>
            <div className="font-medium text-gray-900 dark:text-white">
              {Math.round(volume * 100)}%
            </div>
          </div>
          
          {/* 循环状态 */}
          <div className="text-center">
            <div className="text-gray-500 dark:text-gray-400 mb-1">
              {t('loop')}
            </div>
            <div className={`font-medium ${
              isLooping 
                ? 'text-amber-600 dark:text-amber-400' 
                : 'text-gray-900 dark:text-white'
            }`}>
              {isLooping ? t('on') : t('off')}
            </div>
          </div>
        </div>

        {/* 定时器显示 */}
        {showTimer && (
          <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
            <TimerDisplay variant="full" showProgress={true} />
          </div>
        )}

        {/* 音频分类标签 */}
        {currentSound.category && (
          <div className="pt-2">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
              {t(`categories.${currentSound.category}`)}
            </span>
          </div>
        )}
      </div>
    </motion.div>
  );
}
