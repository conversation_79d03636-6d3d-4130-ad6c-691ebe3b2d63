'use client';

import { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import { useAudioStore } from '@/store/audioStore';
import { PullStringController } from './PullStringController';
import { AudioInfoDisplay } from './AudioInfoDisplay';
import { AmbientInfo } from './AmbientInfo';

interface SleepModePlayerProps {
  className?: string;
}

export function SleepModePlayer({ className = '' }: SleepModePlayerProps) {
  const t = useTranslations('sleepMode');
  const { setPlayerMode } = useAudioStore();
  const [showUI, setShowUI] = useState(true);
  const [lastActivity, setLastActivity] = useState(Date.now());

  // 自动隐藏UI的延迟时间（毫秒）
  const AUTO_HIDE_DELAY = 10000; // 10秒

  // 监听用户活动
  useEffect(() => {
    const handleActivity = () => {
      setLastActivity(Date.now());
      setShowUI(true);
    };

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, []);

  // 自动隐藏UI
  useEffect(() => {
    const timer = setInterval(() => {
      if (Date.now() - lastActivity > AUTO_HIDE_DELAY) {
        setShowUI(false);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [lastActivity]);

  // 退出睡眠模式
  const handleExitSleepMode = () => {
    setPlayerMode('standard');
  };

  // 键盘快捷键
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleExitSleepMode();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, []);

  return (
    <div className={`sleep-mode-player ${className}`}>
      {/* 全屏背景 */}
      <div className="fixed inset-0 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900">
        {/* 背景装饰 */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-amber-400 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-blue-400 rounded-full blur-3xl" />
        </div>
        
        {/* 主要内容区域 */}
        <div className="relative z-10 h-full flex flex-col">
          {/* 顶部控制栏 */}
          <AnimatePresence>
            {showUI && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="absolute top-0 left-0 right-0 z-20 p-4"
              >
                <div className="flex justify-between items-center">
                  <div className="text-white/70 text-sm">
                    {t('sleepMode')}
                  </div>
                  <button
                    onClick={handleExitSleepMode}
                    className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors"
                    aria-label={t('exitSleepMode')}
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* 主内容区域 */}
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="w-full max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 items-center">
                {/* 左侧：环境信息 */}
                <div className="order-2 lg:order-1">
                  <AmbientInfo />
                </div>

                {/* 中央：拉绳控制器 */}
                <div className="order-1 lg:order-2 flex justify-center">
                  <PullStringController />
                </div>

                {/* 右侧：音频信息 */}
                <div className="order-3">
                  <AudioInfoDisplay showTimer={true} />
                </div>
              </div>
            </div>
          </div>

          {/* 底部提示 */}
          <AnimatePresence>
            {showUI && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                className="absolute bottom-0 left-0 right-0 z-20 p-4"
              >
                <div className="text-center text-white/50 text-sm space-y-2">
                  <div>{t('pullStringToControl')}</div>
                  <div className="text-xs">
                    {t('pressEscapeToExit')} • {t('uiAutoHides')}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
