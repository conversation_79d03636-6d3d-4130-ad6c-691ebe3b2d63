'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { motion, useMotionValue, useTransform, PanInfo } from 'framer-motion';
import { useAudioPlayer } from '@/hooks/useAudioPlayer';

interface PullStringControllerProps {
  className?: string;
  onTogglePlay?: () => void;
}

export function PullStringController({ 
  className = '',
  onTogglePlay 
}: PullStringControllerProps) {
  const t = useTranslations('sleepMode');
  const { isPlaying, play, pause } = useAudioPlayer();
  
  const [isDragging, setIsDragging] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);
  const constraintsRef = useRef<HTMLDivElement>(null);
  
  // 拖拽位置状态
  const y = useMotionValue(0);
  const pullDistance = useTransform(y, [0, 100], [0, 100]);
  
  // 绳子的视觉效果
  const stringOpacity = useTransform(y, [0, 50], [0.3, 1]);
  const stringHeight = useTransform(y, [0, 100], [20, 120]);
  
  // 拉环的旋转效果
  const ringRotation = useTransform(y, [0, 100], [0, 15]);
  
  // 触发阈值
  const TRIGGER_THRESHOLD = 60;

  const handleDragStart = () => {
    setIsDragging(true);
    setHasTriggered(false);
  };

  const handleDrag = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const currentY = y.get();
    
    // 检查是否达到触发阈值
    if (currentY >= TRIGGER_THRESHOLD && !hasTriggered) {
      setHasTriggered(true);
      
      // 触发播放/暂停
      if (isPlaying) {
        pause();
      } else {
        play();
      }
      
      // 触觉反馈（如果支持）
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
      
      // 调用外部回调
      onTogglePlay?.();
    }
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    setHasTriggered(false);
    
    // 重置位置
    y.set(0);
  };

  return (
    <div className={`pull-string-controller ${className}`}>
      {/* 控制器容器 */}
      <div 
        ref={constraintsRef}
        className="relative w-full h-64 flex flex-col items-center justify-start pt-8"
      >
        {/* 固定点 */}
        <div className="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full mb-2" />
        
        {/* 绳子 */}
        <motion.div
          className="w-0.5 bg-gray-400 dark:bg-gray-500 origin-top"
          style={{
            height: stringHeight,
            opacity: stringOpacity,
          }}
        />
        
        {/* 拉环 */}
        <motion.div
          drag="y"
          dragConstraints={{ top: 0, bottom: 100 }}
          dragElastic={0.1}
          dragMomentum={false}
          whileDrag={{ scale: 1.1 }}
          onDragStart={handleDragStart}
          onDrag={handleDrag}
          onDragEnd={handleDragEnd}
          style={{
            y,
            rotate: ringRotation,
          }}
          className="pull-string draggable"
          data-drag="true"
          data-testid="pull-string"
          className="relative cursor-grab active:cursor-grabbing"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {/* 拉环主体 */}
          <div className={`
            w-16 h-16 rounded-full border-4 border-gray-300 dark:border-gray-600
            bg-white dark:bg-gray-800 shadow-lg
            flex items-center justify-center
            transition-colors duration-200
            ${isDragging ? 'border-amber-400 dark:border-amber-500' : ''}
            ${hasTriggered ? 'border-green-400 dark:border-green-500' : ''}
          `}>
            {/* 拉环内部图标 */}
            <div className={`
              w-8 h-8 rounded-full
              flex items-center justify-center
              transition-colors duration-200
              ${isPlaying 
                ? 'bg-amber-100 dark:bg-amber-900/20 text-amber-600 dark:text-amber-400' 
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
              }
            `}>
              {isPlaying ? (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              )}
            </div>
          </div>
          
          {/* 拖拽提示 */}
          {isDragging && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="absolute top-20 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
            >
              <div className="px-3 py-1 bg-black/70 text-white text-xs rounded-full">
                {pullDistance.get() >= TRIGGER_THRESHOLD 
                  ? t('releaseToToggle') 
                  : t('pullToToggle')
                }
              </div>
            </motion.div>
          )}
        </motion.div>
        
        {/* 状态指示器 */}
        <div className="mt-8 text-center">
          <div className={`
            text-sm font-medium transition-colors duration-200
            ${isPlaying 
              ? 'text-amber-600 dark:text-amber-400' 
              : 'text-gray-500 dark:text-gray-400'
            }
          `}>
            {isPlaying ? t('playing') : t('paused')}
          </div>
          
          {/* 操作提示 */}
          <div className="text-xs text-gray-400 dark:text-gray-500 mt-2">
            {t('pullStringHint')}
          </div>
        </div>
      </div>
    </div>
  );
}
