'use client';

import { useEffect } from 'react';
import { useAudioStore } from '@/store/audioStore';
import { StandardPlayer } from './StandardPlayer';

interface AudioPlayerProviderProps {
  children: React.ReactNode;
  className?: string;
}

export function AudioPlayerProvider({ 
  children, 
  className 
}: AudioPlayerProviderProps) {
  const { 
    currentSound, 
    playerUI, 
    setPlayerVisible 
  } = useAudioStore();

  // 监听音频播放状态，自动显示/隐藏播放器
  useEffect(() => {
    console.log('🔍 AudioPlayerProvider useEffect:', {
      currentSound: currentSound?.title,
      isVisible: playerUI.isVisible,
      mode: playerUI.mode
    });

    if (currentSound && !playerUI.isVisible) {
      console.log('🚀 显示播放器');
      setPlayerVisible(true);
    }
  }, [currentSound, playerUI.isVisible, setPlayerVisible]);

  return (
    <div className={className}>
      {children}
      
      {/* 标准播放器 - 只在标准模式下显示 */}
      {playerUI.mode === 'standard' && (
        <StandardPlayer
          position={playerUI.position}
          showMixingButton={true}
          showSleepModeButton={true}
          autoHide={false}
        />
      )}
      
      {/* 睡眠模式播放器 - 将在后续阶段实现 */}
      {playerUI.mode === 'sleep' && (
        <div className="fixed inset-0 z-50 bg-gray-900 flex items-center justify-center">
          <div className="text-white text-center">
            <h2 className="text-2xl font-bold mb-4">睡眠模式</h2>
            <p className="text-gray-300 mb-8">睡眠模式界面将在第三阶段实现</p>
            <button
              onClick={() => useAudioStore.getState().setPlayerMode('standard')}
              className="px-6 py-3 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors"
            >
              返回标准模式
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
