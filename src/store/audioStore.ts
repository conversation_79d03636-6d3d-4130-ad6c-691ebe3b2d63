import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { MultilingualAudioItem, AudioPlayState, MixingChannel, TimerConfig } from '@/types/audio';

interface AudioStore {
  // 当前播放状态
  currentSound: MultilingualAudioItem | null;
  playState: AudioPlayState;

  // 播放器UI状态
  playerUI: {
    mode: 'standard' | 'sleep';
    isVisible: boolean;
    position: 'bottom' | 'top' | 'floating';
    isMinimized: boolean;
    showTimerPanel: boolean;
    showMixingPanel: boolean;
  };

  // 混音功能 (MVP版本限制2个音频)
  mixingChannels: MixingChannel[];
  maxChannels: number;
  masterVolume: number;

  // 定时器
  timer: TimerConfig;

  // 用户偏好
  favorites: string[];
  recentlyPlayed: string[];
  userVolume: number;
  
  // 播放控制方法
  setCurrentSound: (sound: MultilingualAudioItem | null) => void;
  updatePlayState: (state: Partial<AudioPlayState>) => void;

  // 播放器UI控制方法
  setPlayerMode: (mode: 'standard' | 'sleep') => void;
  setPlayerVisible: (visible: boolean) => void;
  setPlayerPosition: (position: 'bottom' | 'top' | 'floating') => void;
  togglePlayerMinimized: () => void;
  setTimerPanelVisible: (visible: boolean) => void;
  setMixingPanelVisible: (visible: boolean) => void;
  
  // 混音控制方法
  addMixingChannel: (sound: MultilingualAudioItem) => boolean;
  removeMixingChannel: (channelId: string) => void;
  updateChannelVolume: (channelId: string, volume: number) => void;
  setMasterVolume: (volume: number) => void;
  
  // 定时器方法
  setTimer: (duration: number) => void;
  clearTimer: () => void;
  updateTimerRemaining: (remaining: number) => void;
  
  // 用户偏好方法
  addToFavorites: (soundId: string) => void;
  removeFromFavorites: (soundId: string) => void;
  addToRecentlyPlayed: (soundId: string) => void;
  setUserVolume: (volume: number) => void;
}

export const useAudioStore = create<AudioStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentSound: null,
      playState: {
        isPlaying: false,
        isPaused: false,
        isLoading: false,
        currentTime: 0,
        duration: 0,
        volume: 0.7,
        isLooping: false,
      },

      // 播放器UI初始状态
      playerUI: {
        mode: 'standard',
        isVisible: false,
        position: 'bottom',
        isMinimized: false,
        showTimerPanel: false,
        showMixingPanel: false,
      },
      
      mixingChannels: [],
      maxChannels: 2, // MVP版本限制
      masterVolume: 0.8,
      
      timer: {
        duration: 0,
        isActive: false,
        remainingTime: 0,
        fadeOutDuration: 10,
        autoStop: true,
      },
      
      favorites: [],
      recentlyPlayed: [],
      userVolume: 0.7,
      
      // 播放控制方法
      setCurrentSound: (sound) => {
        console.log('🎵 设置当前音频:', { sound: sound?.title, isVisible: get().playerUI.isVisible });
        set({ currentSound: sound });
        if (sound) {
          get().addToRecentlyPlayed(sound.id);
          // 自动显示播放器
          console.log('👁️ 显示播放器');
          get().setPlayerVisible(true);
        }
      },
      
      updatePlayState: (newState) => {
        set((state) => ({
          playState: { ...state.playState, ...newState }
        }));
      },
      
      // 混音控制方法
      addMixingChannel: (sound) => {
        const { mixingChannels, maxChannels } = get();
        
        if (mixingChannels.length >= maxChannels) {
          console.warn(`MVP版本最多支持${maxChannels}个音频同时播放`);
          return false;
        }
        
        const newChannel: MixingChannel = {
          id: `channel_${Date.now()}`,
          soundId: sound.id,
          volume: 0.7,
          isMuted: false,
          isActive: true,
        };
        
        set((state) => ({
          mixingChannels: [...state.mixingChannels, newChannel]
        }));
        
        return true;
      },
      
      removeMixingChannel: (channelId) => {
        set((state) => ({
          mixingChannels: state.mixingChannels.filter(channel => channel.id !== channelId)
        }));
      },
      
      updateChannelVolume: (channelId, volume) => {
        set((state) => ({
          mixingChannels: state.mixingChannels.map(channel =>
            channel.id === channelId ? { ...channel, volume } : channel
          )
        }));
      },
      
      setMasterVolume: (volume) => {
        set({ masterVolume: volume });
      },
      
      // 定时器方法
      setTimer: (duration) => {
        set({
          timer: {
            duration,
            isActive: true,
            remainingTime: duration * 60, // 转换为秒
            fadeOutDuration: 10,
            autoStop: true,
          }
        });
      },
      
      clearTimer: () => {
        set((state) => ({
          timer: { ...state.timer, isActive: false, remainingTime: 0 }
        }));
      },
      
      updateTimerRemaining: (remaining) => {
        set((state) => ({
          timer: { ...state.timer, remainingTime: remaining }
        }));
      },
      
      // 用户偏好方法
      addToFavorites: (soundId) => {
        set((state) => ({
          favorites: state.favorites.includes(soundId) 
            ? state.favorites 
            : [...state.favorites, soundId]
        }));
      },
      
      removeFromFavorites: (soundId) => {
        set((state) => ({
          favorites: state.favorites.filter(id => id !== soundId)
        }));
      },
      
      addToRecentlyPlayed: (soundId) => {
        set((state) => {
          const filtered = state.recentlyPlayed.filter(id => id !== soundId);
          return {
            recentlyPlayed: [soundId, ...filtered].slice(0, 20) // 保留最近20个
          };
        });
      },
      
      setUserVolume: (volume) => {
        set({ userVolume: volume });
      },

      // 播放器UI控制方法实现
      setPlayerMode: (mode) => {
        set((state) => ({
          playerUI: { ...state.playerUI, mode }
        }));
      },

      setPlayerVisible: (visible) => {
        console.log('👁️ 设置播放器可见性:', visible);
        set((state) => ({
          playerUI: { ...state.playerUI, isVisible: visible }
        }));
      },

      setPlayerPosition: (position) => {
        set((state) => ({
          playerUI: { ...state.playerUI, position }
        }));
      },

      togglePlayerMinimized: () => {
        set((state) => ({
          playerUI: { ...state.playerUI, isMinimized: !state.playerUI.isMinimized }
        }));
      },

      setTimerPanelVisible: (visible) => {
        set((state) => ({
          playerUI: { ...state.playerUI, showTimerPanel: visible }
        }));
      },

      setMixingPanelVisible: (visible) => {
        set((state) => ({
          playerUI: { ...state.playerUI, showMixingPanel: visible }
        }));
      },
    }),
    {
      name: 'noisesleep-audio-store',
      partialize: (state) => ({
        favorites: state.favorites,
        recentlyPlayed: state.recentlyPlayed,
        userVolume: state.userVolume,
        masterVolume: state.masterVolume,
        playerUI: state.playerUI,
      }),
    }
  )
);
