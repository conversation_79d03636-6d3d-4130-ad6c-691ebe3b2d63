#!/bin/bash
# scripts/verify-cdn.sh
# CDN部署验证脚本 - 动态扫描本地音频文件并验证CDN部署

set -e

# 配置
CDN_BASE_URL="https://cdn.noisesleep.com/sounds"
LOCAL_SOUNDS_DIR="/Users/<USER>/Documents/NoiseSleep/public/sounds"
TIMEOUT_SECONDS=10
REPORT_FILE="cdn_verification_report_$(date +%Y%m%d_%H%M%S).md"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 统计变量
all_files=()
failed_files=()
success_files=()
categories=()
category_totals=()
category_success=()
category_failed=()
total_files=0
success_count=0
total_size=0
total_time=0

# 分类统计函数
find_category_index() {
    local category="$1"
    local i=0
    for cat in "${categories[@]}"; do
        if [ "$cat" = "$category" ]; then
            echo $i
            return 0
        fi
        ((i++))
    done
    echo -1
}

add_category_stats() {
    local category="$1"
    local index=$(find_category_index "$category")

    if [ $index -eq -1 ]; then
        categories+=("$category")
        category_totals+=(1)
        category_success+=(0)
        category_failed+=(0)
    else
        ((category_totals[index]++))
    fi
}

update_category_success() {
    local category="$1"
    local index=$(find_category_index "$category")
    if [ $index -ne -1 ]; then
        ((category_success[index]++))
    fi
}

update_category_failed() {
    local category="$1"
    local index=$(find_category_index "$category")
    if [ $index -ne -1 ]; then
        ((category_failed[index]++))
    fi
}

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🔍 $1${NC}"
}

# 检查本地音频目录
check_local_directory() {
    log_header "检查本地音频目录..."

    if [ ! -d "$LOCAL_SOUNDS_DIR" ]; then
        log_error "本地音频目录不存在: $LOCAL_SOUNDS_DIR"
        log_error "请确认项目路径正确，或音频文件已正确放置"
        exit 1
    fi

    log_success "本地音频目录存在: $LOCAL_SOUNDS_DIR"
}

# 扫描本地音频文件
scan_local_files() {
    log_header "扫描本地音频文件..."

    # 使用find命令递归查找音频文件
    while IFS= read -r -d '' file; do
        # 获取相对于sounds目录的路径
        relative_path="${file#$LOCAL_SOUNDS_DIR/}"
        all_files+=("$relative_path")

        # 提取分类名（目录名）
        category=$(dirname "$relative_path")
        if [ "$category" = "." ]; then
            category="root"
        fi

        # 统计分类
        add_category_stats "$category"

    done < <(find "$LOCAL_SOUNDS_DIR" -type f \( -name "*.mp3" -o -name "*.wav" \) -print0)

    total_files=${#all_files[@]}

    if [ $total_files -eq 0 ]; then
        log_error "未找到任何音频文件"
        log_error "请检查音频文件是否存在于: $LOCAL_SOUNDS_DIR"
        exit 1
    fi

    log_success "发现 $total_files 个音频文件"

    # 显示分类统计
    echo ""
    log_info "按分类统计:"
    local i=0
    for category in "${categories[@]}"; do
        echo "  📁 $category: ${category_totals[i]} 个文件"
        ((i++))
    done
    echo ""
}

# 测试CDN域名解析
test_cdn_domain() {
    log_header "测试CDN域名解析..."

    if nslookup cdn.noisesleep.com > /dev/null 2>&1; then
        log_success "CDN域名解析成功"
    else
        log_error "CDN域名解析失败"
        log_error "请检查域名配置或网络连接"
        exit 1
    fi
}

# 验证单个文件
verify_file() {
    local file="$1"
    local url="$CDN_BASE_URL/$file"

    echo "🔍 测试: $file"

    # 使用curl测试文件访问，设置超时
    local response
    response=$(curl -s -o /dev/null -w "%{http_code},%{time_total},%{size_download},%{time_connect}" \
        --max-time $TIMEOUT_SECONDS \
        --connect-timeout 5 \
        "$url" 2>/dev/null)

    local curl_exit_code=$?

    if [ $curl_exit_code -ne 0 ]; then
        echo "  ❌ 网络错误 - curl退出码: $curl_exit_code"
        failed_files+=("$file:NETWORK_ERROR")

        # 更新分类统计
        local category=$(dirname "$file")
        [ "$category" = "." ] && category="root"
        update_category_failed "$category"
        return 1
    fi

    IFS=',' read -r http_code time_total size_download time_connect <<< "$response"

    if [ "$http_code" = "200" ]; then
        echo "  ✅ 成功 - 状态码: $http_code, 大小: ${size_download}字节, 耗时: ${time_total}秒"
        success_files+=("$file")
        ((success_count++))

        # 累计统计
        total_size=$((total_size + size_download))
        total_time=$(echo "$total_time + $time_total" | bc -l 2>/dev/null || echo "$total_time")

        # 更新分类统计
        local category=$(dirname "$file")
        [ "$category" = "." ] && category="root"
        update_category_success "$category"

        return 0
    else
        echo "  ❌ 失败 - 状态码: $http_code"
        failed_files+=("$file:HTTP_$http_code")

        # 更新分类统计
        local category=$(dirname "$file")
        [ "$category" = "." ] && category="root"
        update_category_failed "$category"

        return 1
    fi
}

# 验证所有文件
verify_all_files() {
    log_header "开始验证CDN上的音频文件..."
    echo ""

    local file_count=0
    for file in "${all_files[@]}"; do
        ((file_count++))
        echo "[$file_count/$total_files]"
        verify_file "$file"
        echo ""

        # 每10个文件显示一次进度
        if [ $((file_count % 10)) -eq 0 ]; then
            log_info "已验证 $file_count/$total_files 个文件..."
        fi
    done
}

# 生成验证报告
generate_report() {
    log_header "生成验证报告..."

    local success_rate=0
    if [ $total_files -gt 0 ]; then
        success_rate=$(echo "scale=2; $success_count * 100 / $total_files" | bc -l 2>/dev/null || echo "0")
    fi

    local avg_time=0
    if [ $success_count -gt 0 ] && command -v bc >/dev/null 2>&1; then
        avg_time=$(echo "scale=3; $total_time / $success_count" | bc -l 2>/dev/null || echo "0")
    fi

    local avg_size=0
    if [ $success_count -gt 0 ]; then
        avg_size=$((total_size / success_count))
    fi

    # 控制台报告
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_header "CDN部署验证报告"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""

    # 总体统计
    log_info "📊 总体统计:"
    echo "  🎵 总文件数: $total_files"
    echo "  ✅ 成功验证: $success_count"
    echo "  ❌ 验证失败: $((total_files - success_count))"
    echo "  📈 成功率: ${success_rate}%"
    echo "  ⏱️  平均响应时间: ${avg_time}秒"
    echo "  📦 平均文件大小: $(numfmt --to=iec $avg_size 2>/dev/null || echo "${avg_size}字节")"
    echo ""

    # 分类统计
    log_info "📁 分类统计:"
    local i=0
    for category in "${categories[@]}"; do
        local total=${category_totals[i]}
        local success=${category_success[i]}
        local failed=${category_failed[i]}
        local cat_success_rate=0
        if [ $total -gt 0 ]; then
            cat_success_rate=$(echo "scale=1; $success * 100 / $total" | bc -l 2>/dev/null || echo "0")
        fi
        echo "  📂 $category: $success/$total (${cat_success_rate}%)"
        ((i++))
    done
    echo ""

    # 失败文件详情
    if [ ${#failed_files[@]} -gt 0 ]; then
        log_warning "❌ 验证失败的文件:"
        for failed_file in "${failed_files[@]}"; do
            IFS=':' read -r file error <<< "$failed_file"
            echo "  - $file (错误: $error)"
        done
        echo ""
    fi

    # 成功文件列表（仅在全部成功时显示）
    if [ $success_count -eq $total_files ]; then
        log_success "✅ 所有文件验证成功:"
        for success_file in "${success_files[@]}"; do
            echo "  ✓ $success_file"
        done
        echo ""
    fi
}

# 生成Markdown报告文件
generate_markdown_report() {
    local timestamp=$(date '+%Y年%m月%d日 %H:%M:%S')
    local success_rate=0
    if [ $total_files -gt 0 ]; then
        success_rate=$(echo "scale=2; $success_count * 100 / $total_files" | bc -l 2>/dev/null || echo "0")
    fi

    cat > "$REPORT_FILE" << EOF
# NoiseSleep CDN部署验证报告

**生成时间**: $timestamp
**CDN基础URL**: $CDN_BASE_URL
**本地音频目录**: $LOCAL_SOUNDS_DIR

## 📊 验证结果概览

| 指标 | 数值 |
|------|------|
| 总文件数 | $total_files |
| 成功验证 | $success_count |
| 验证失败 | $((total_files - success_count)) |
| 成功率 | ${success_rate}% |
| 总文件大小 | $(numfmt --to=iec $total_size 2>/dev/null || echo "${total_size}字节") |

## 📁 分类统计

| 分类 | 总数 | 成功 | 失败 | 成功率 |
|------|------|------|------|--------|
EOF

    local i=0
    for category in "${categories[@]}"; do
        local total=${category_totals[i]}
        local success=${category_success[i]}
        local failed=${category_failed[i]}
        local cat_success_rate=0
        if [ $total -gt 0 ]; then
            cat_success_rate=$(echo "scale=1; $success * 100 / $total" | bc -l 2>/dev/null || echo "0")
        fi
        echo "| $category | $total | $success | $failed | ${cat_success_rate}% |" >> "$REPORT_FILE"
        ((i++))
    done

    if [ ${#failed_files[@]} -gt 0 ]; then
        cat >> "$REPORT_FILE" << EOF

## ❌ 验证失败的文件

| 文件路径 | 错误类型 |
|----------|----------|
EOF
        for failed_file in "${failed_files[@]}"; do
            IFS=':' read -r file error <<< "$failed_file"
            echo "| $file | $error |" >> "$REPORT_FILE"
        done
    fi

    cat >> "$REPORT_FILE" << EOF

## 📋 建议的下一步操作

EOF

    if [ $success_count -eq $total_files ]; then
        cat >> "$REPORT_FILE" << EOF
### ✅ 验证完全成功

所有音频文件都可以通过CDN正常访问，可以安全地切换应用到CDN模式：

1. **更新环境变量**:
   \`\`\`bash
   export NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds
   export NEXT_PUBLIC_CDN_PERCENTAGE=100
   \`\`\`

2. **重新构建应用**:
   \`\`\`bash
   npm run build
   \`\`\`

3. **部署到生产环境**

4. **监控应用性能和错误日志**

### 🎯 性能优化建议

- 启用音频预加载: \`NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD=true\`
- 监控CDN缓存命中率
- 设置音频文件的适当缓存策略
EOF
    else
        cat >> "$REPORT_FILE" << EOF
### ⚠️ 部分验证失败

需要修复以下问题后再切换到CDN模式：

#### 🔧 故障排除步骤

1. **检查R2存储桶**:
   - 登录Cloudflare Dashboard
   - 验证存储桶中的文件路径和权限
   - 确认失败的文件是否存在

2. **验证CDN配置**:
   - 检查自定义域名 cdn.noisesleep.com 配置
   - 验证DNS解析是否正确
   - 确认SSL/TLS设置

3. **重新上传失败的文件**:
   \`\`\`bash
   # 重新运行上传脚本
   ./scripts/upload-to-cdn.sh
   \`\`\`

4. **检查网络连接**:
   - 测试本地网络连接
   - 验证防火墙设置
   - 检查Cloudflare服务状态

#### 🚨 如果问题持续存在

- 联系Cloudflare技术支持
- 检查账户配额和限制
- 考虑使用备用CDN服务
EOF
    fi

    log_success "详细报告已保存到: $REPORT_FILE"
}

# 提供最终建议和下一步操作
provide_final_recommendations() {
    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    if [ $success_count -eq $total_files ]; then
        log_success "🎉 CDN部署验证完全成功！"
        echo ""
        log_info "📋 立即可执行的下一步操作:"
        echo "1. 更新环境变量切换到CDN模式:"
        echo "   export NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds"
        echo "   export NEXT_PUBLIC_CDN_PERCENTAGE=100"
        echo ""
        echo "2. 重新构建应用:"
        echo "   npm run build"
        echo ""
        echo "3. 启动开发服务器测试:"
        echo "   npm run dev"
        echo ""
        echo "4. 部署到生产环境"
        echo ""
        log_info "🎯 性能优化建议:"
        echo "- 启用音频预加载: export NEXT_PUBLIC_ENABLE_AUDIO_PRELOAD=true"
        echo "- 监控CDN性能指标"
        echo "- 设置适当的缓存策略"

        return 0
    else
        log_error "⚠️  CDN部署验证未完全通过"
        echo ""
        log_info "🔧 需要修复的问题:"
        echo "- 总计 $((total_files - success_count)) 个文件无法访问"
        echo "- 成功率: $(echo "scale=1; $success_count * 100 / $total_files" | bc -l 2>/dev/null || echo "0")%"
        echo ""
        log_info "📋 建议的修复步骤:"
        echo "1. 检查失败文件的具体错误信息（见上方详细报告）"
        echo "2. 重新上传失败的文件:"
        echo "   ./scripts/upload-to-cdn.sh"
        echo "3. 验证Cloudflare R2存储桶配置"
        echo "4. 检查CDN域名和DNS设置"
        echo "5. 重新运行验证:"
        echo "   ./scripts/verify-cdn.sh"
        echo ""
        log_warning "⚠️  建议修复所有问题后再切换到CDN模式"

        return 1
    fi
}

# 主执行流程
main() {
    echo ""
    echo "🧪 NoiseSleep CDN部署验证脚本"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""

    # 执行验证步骤
    check_local_directory
    scan_local_files
    test_cdn_domain
    verify_all_files
    generate_report
    generate_markdown_report
    provide_final_recommendations

    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    log_info "验证完成！详细报告: $REPORT_FILE"
    echo ""

    # 返回适当的退出码
    if [ $success_count -eq $total_files ]; then
        exit 0
    else
        exit 1
    fi
}

# 运行主函数
main "$@"
