#!/bin/bash
# scripts/verify-cdn.sh
# CDN部署验证脚本

CDN_BASE_URL="https://cdn.noisesleep.com/sounds"

echo "🧪 开始验证CDN部署..."

# 测试文件列表 (基于实际存在的文件)
declare -a test_files=(
    "rain/gentle-rain.wav"
    "rain/heavy-rain.mp3"
    "rain/heavy-rain.wav"
    "rain/light-rain.mp3"
    "rain/rain-drops.wav"
    "nature/campfire.mp3"
    "nature/forest-birds.wav"
    "nature/wind-leaves.wav"
    "ocean/ocean-waves.wav"
    "noise/pink-noise.wav"
)

success_count=0
total_count=${#test_files[@]}
failed_files=()

echo "📋 测试文件列表 ($total_count 个文件):"
for file in "${test_files[@]}"; do
    echo "  - $file"
done
echo ""

# 测试CDN域名解析
echo "🔍 测试CDN域名解析..."
if nslookup cdn.noisesleep.com > /dev/null 2>&1; then
    echo "✅ CDN域名解析成功"
else
    echo "❌ CDN域名解析失败"
    exit 1
fi

# 测试每个音频文件
for file in "${test_files[@]}"; do
    url="$CDN_BASE_URL/$file"
    echo "🔍 测试: $file"
    
    # 使用curl测试文件访问
    response=$(curl -s -o /dev/null -w "%{http_code},%{time_total},%{size_download}" "$url")
    IFS=',' read -r http_code time_total size_download <<< "$response"
    
    if [ "$http_code" = "200" ]; then
        echo "  ✅ 成功 - 状态码: $http_code, 大小: ${size_download}字节, 耗时: ${time_total}秒"
        ((success_count++))
    else
        echo "  ❌ 失败 - 状态码: $http_code"
        failed_files+=("$file")
    fi
done

echo ""
echo "📊 验证结果: $success_count/$total_count 文件可访问"

if [ $success_count -eq $total_count ]; then
    echo "🎉 CDN部署验证成功！"
    
    # 性能测试
    echo ""
    echo "⚡ 性能测试..."
    test_url="$CDN_BASE_URL/rain/light-rain.mp3"
    perf_result=$(curl -s -o /dev/null -w "连接时间: %{time_connect}s, 首字节时间: %{time_starttransfer}s, 总时间: %{time_total}s" "$test_url")
    echo "📈 $perf_result"
    
    echo ""
    echo "✅ 所有测试通过！可以安全地切换到CDN。"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 更新环境变量: export NEXT_PUBLIC_AUDIO_CDN_URL=https://cdn.noisesleep.com/sounds"
    echo "2. 重启应用服务"
    echo "3. 监控应用性能和错误日志"
    
    exit 0
else
    echo "⚠️  部分文件无法访问，请检查配置"
    echo ""
    echo "❌ 失败的文件:"
    for file in "${failed_files[@]}"; do
        echo "  - $file"
    done
    echo ""
    echo "🔧 排查建议:"
    echo "1. 检查R2存储桶中的文件路径和权限"
    echo "2. 验证CDN域名配置是否正确"
    echo "3. 确认文件上传是否完整"
    echo "4. 检查Cloudflare缓存设置"
    
    exit 1
fi
